<template>
  <div class="document-manage-container" ref="containerRef">
    <div
      class="document-manage-layout"
      :style="{ gridTemplateColumns: `${leftWidth}px 4px 1fr` }"
    >
      <!-- 左侧项目阶段树 -->
      <div class="tree-section">
        <div class="tree-header">
          <div class="tree-title">项目阶段</div>
          <el-button type="primary" size="small" link @click="handleTreeReset">
            重置
          </el-button>
        </div>
        <div class="tree-container">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            :default-expanded-keys="['all']"
            @current-change="handleTreeNodeClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node-content">
                <svg
                  style="margin-right: 8px"
                  viewBox="0 0 16 16"
                  width="16"
                  height="16"
                >
                  <path
                    :d="`${
                      data.isLeaf
                        ? 'M13,6 L9,6 L9,5 L9,2 L3,2 L3,14 L13,14 L13,6 Z M12.5857864,5 L10,2.41421356 L10,5 L12.5857864,5 Z M2,1 L10,1 L14,5 L14,15 L2,15 L2,1 Z'
                        : 'M16,6 L14,14 L2,14 L0,6 L16,6 Z M14.7192236,7 L1.28077641,7 L2.78077641,13 L13.2192236,13 L14.7192236,7 Z M6,1 L8,3 L15,3 L15,5 L14,5 L14,4 L7.58578644,4 L5.58578644,2 L2,2 L2,5 L1,5 L1,1 L6,1 Z'
                    }`"
                    stroke-width="1"
                    fill="#8a8e99"
                  ></path>
                </svg>
                <span>{{ node.label }}</span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 拖拽分割条 -->
      <div class="resize-handle" @mousedown="handleMouseDown"></div>

      <!-- 右侧内容区域 -->
      <div class="content-section">


        <!-- 项目资料表格 -->
        <div class="document-list-section">
          <div class="table-container">
            <im-table
              ref="documentTableRef"
              :data="documentList"
              :columns="documentColumns"
              :pagination="paginationConfig"
              :loading="loading"
              :toolbar="toolbarConfig"
              :column-storage="createColumnStorage('document_manage', 'local')"
              :height="666"
              stripe
              border
              center
              show-index
              show-checkbox
              show-overflow-tooltip
              @on-page-change="handlePageChange"
              @on-page-size-change="handlePageSizeChange"
              @sort-change="handleSortChange"
              @selection-change="handleSelectionChange"
            >
              <!-- 工具栏左侧 -->
              <template #toolbar-left="{ checkedRows }">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchExport(checkedRows)"
                >
                  导出
                </el-button>
                <el-input
                  v-model="searchKeyword"
                  placeholder="请输入关键字"
                  class="search-input"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #suffix>
                    <el-icon class="search-icon" @click="handleSearch">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </template>
              <!-- 工具栏右侧 -->
              <template #toolbar-right="{ checkedRows }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleBatchExport(checkedRows)"
                >
                  上传
                </el-button>
                <el-button type="primary" size="small" @click="handleDownload">
                  批量下载
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchDelete(checkedRows)"
                >
                  批量删除
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchAnalysis(checkedRows)"
                >
                  批量解析
                </el-button>
              </template>

              <!-- 序号列 -->
              <template #sequence="{ row }">
                <span class="sequence-text">{{ row.sequence }}</span>
              </template>

              <!-- 资料名称列 -->
              <template #documentName="{ row }">
                <span class="document-name-text">{{ row.documentName }}</span>
              </template>

              <!-- 更新人列 -->
              <template #updater="{ row }">
                <span class="updater-text">{{ row.updater }}</span>
              </template>

              <!-- 更新时间列 -->
              <template #updateTime="{ row }">
                <span class="update-time-text">{{ row.updateTime }}</span>
              </template>

              <!-- 操作列 -->
              <template #operation="{ row }">
                <div class="operation-buttons">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleView(row)"
                  >
                    查看
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </im-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { createColumnStorage } from "@/components/ItsmCommon";
import {
  getDocumentList,
  deleteDocument,
  batchDeleteDocuments,
  exportDocuments,
  mockTreeData,
  type DocumentData,
  type DocumentQueryParams,
  type TreeNodeData
} from "./api";

// Props
interface Props {
  projectId?: string;
}

const props = defineProps<Props>();

// 响应式数据
const documentTableRef = ref();
const treeRef = ref();
const containerRef = ref();
const loading = ref(false);
const documentList = ref<DocumentData[]>([]);
const totalCount = ref(0);
const searchKeyword = ref("");
const selectedStageId = ref("all");
const selectedRows = ref<DocumentData[]>([]);
const treeData = ref<TreeNodeData[]>(mockTreeData);

// 布局相关
const leftWidth = ref(240);
const isDragging = ref(false);

// 树组件配置
const treeProps = {
  label: "label",
  children: "children"
};

// 查询参数
const queryParams = reactive<DocumentQueryParams>({
  currentPage: 1,
  pageSize: 10,
  projectId: props.projectId,
  stageId: "all"
});

// 分页配置
const paginationConfig = computed(() => ({
  hideOnEmptyData: true,
  total: totalCount.value,
  currentPage: queryParams.currentPage,
  pageSize: queryParams.pageSize,
  pageSizes: [10, 20, 50, 100]
}));

// 工具栏配置
const toolbarConfig = reactive({
  showColumnSetting: true,
  showRefresh: true,
  showDensity: true,
  showFullscreen: true
});

// 表格列配置
const documentColumns = [
  {
    prop: "documentName",
    label: "附件名称",
    minWidth: 200,
    align: "left",
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: "updater",
    label: "更新人",
    width: 120,
    align: "center",
    sortable: true
  },
  {
    prop: "updateTime",
    label: "更新时间",
    width: 180,
    align: "center",
    sortable: true
  },
  {
    prop: "operation",
    label: "操作",
    width: 200,
    align: "center",
    fixed: "right",
    sortable: false
  }
];

// 事件处理
const handlePageChange = async (pageOrSize: number, pageSize: number, query?: any) => {
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== queryParams.pageSize) {
    // 页码变化
    queryParams.currentPage = pageOrSize;
    await loadDocumentData();
  }
  // 分页大小变化在handlePageSizeChange中处理
};

const handlePageSizeChange = async (currentPage: number, size: number, query?: any) => {
  queryParams.pageSize = size;
  queryParams.currentPage = 1; // 分页大小变化时重置到第一页
  await loadDocumentData();
};

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  queryParams.sortField = prop;
  queryParams.sortOrder = order === "ascending" ? "asc" : "desc";
  loadDocumentData();
};

const handleSelectionChange = (selection: DocumentData[]) => {
  selectedRows.value = selection;
  console.log("Selection changed:", selection);
};

const handleTreeNodeClick = (nodeData: TreeNodeData) => {
  selectedStageId.value = nodeData.id;
  queryParams.stageId = nodeData.id;
  queryParams.currentPage = 1;
  loadDocumentData();
};

const handleTreeReset = () => {
  // 重置树选择到"全部"
  selectedStageId.value = "all";
  queryParams.stageId = "all";
  queryParams.currentPage = 1;

  // 设置树的当前选中节点
  if (treeRef.value) {
    treeRef.value.setCurrentKey("all");
  }

  // 重新加载数据
  loadDocumentData();
  ElMessage.success("已重置到全部阶段");
};

const handleSearch = () => {
  queryParams.searchKeyword = searchKeyword.value;
  queryParams.currentPage = 1;
  loadDocumentData();
};

const handleView = (row: DocumentData) => {
  ElMessage.info(`查看文档: ${row.documentName}`);
};

const handleEdit = (row: DocumentData) => {
  ElMessage.info(`编辑文档: ${row.documentName}`);
};

const handleDelete = async (row: DocumentData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档"${row.documentName}"吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    await deleteDocument(row.id);
    ElMessage.success("删除成功");
    await loadDocumentData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
      console.error("Delete error:", error);
    }
  }
};

const handleBatchExport = async (rows: DocumentData[]) => {
  try {
    if (rows.length === 0) {
      ElMessage.warning("请选择要导出的数据");
      return;
    }
    const ids = rows.map(row => row.id);
    const result = await exportDocuments(ids);
    if (result.success) {
      ElMessage.success(`导出${rows.length}条项目资料记录成功`);
    }
  } catch (error) {
    ElMessage.error("导出失败");
    console.error("Export error:", error);
  }
};

const handleDownload = () => {
  ElMessage.info("配置下载功能");
};

const handleBatchDelete = async (rows: DocumentData[]) => {
  try {
    if (rows.length === 0) {
      ElMessage.warning("请选择要删除的数据");
      return;
    }

    await ElMessageBox.confirm(
      `确定要删除选中的${rows.length}条项目资料吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const ids = rows.map(row => row.id);
    await batchDeleteDocuments(ids);
    ElMessage.success(`成功删除${rows.length}条项目资料`);
    await loadDocumentData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
      console.error("Batch delete error:", error);
    }
  }
};

const handleBatchAnalysis = (rows: DocumentData[]) => {
  if (rows.length === 0) {
    ElMessage.warning("请选择要解析的数据");
    return;
  }
  ElMessage.success(`开始解析${rows.length}条项目资料`);
  // 这里可以实现具体的批量解析逻辑
};

// 拖拽处理方法
const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true;
  const startX = e.clientX;
  const startWidth = leftWidth.value;

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return;

    const deltaX = e.clientX - startX;
    const newWidth = startWidth + deltaX;

    // 限制最小和最大宽度
    const minWidth = 200;
    const maxWidth = containerRef.value
      ? containerRef.value.clientWidth * 0.6
      : 600;

    leftWidth.value = Math.max(minWidth, Math.min(maxWidth, newWidth));
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
  document.body.style.cursor = "col-resize";
  document.body.style.userSelect = "none";
};

// 数据加载
const loadDocumentData = async () => {
  try {
    loading.value = true;
    const response = await getDocumentList(queryParams);
    if (response.success) {
      documentList.value = response.rows || [];
      totalCount.value = response.total || 0;
    }
  } catch (error) {
    ElMessage.error("加载项目资料数据失败");
    console.error("Load document data error:", error);
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(async () => {
  await loadDocumentData();
});

// 组件选项
defineOptions({
  name: "DocumentManage"
});
</script>

<style lang="scss" scoped>
.document-manage-container {
  padding: 0;
  height: 100%;
  overflow: hidden;

  .document-manage-layout {
    display: grid;
    height: 100%;
    gap: 0;

    .tree-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 16px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .tree-title {
          color: var(--el-color-primary);
          font-size: 14px;
          font-weight: 400;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;
          min-height: auto;
        }
      }

      .tree-container {
        height: calc(100% - 50px);
        overflow-y: auto;

        :deep(.el-tree) {
          background: transparent;

          .el-tree-node {
            .el-tree-node__content {
              color: rgba(255, 255, 255, 0.85);
              background: transparent;
              padding: 8px 4px;
              border-radius: 4px;

              &:hover {
                background-color: rgba(255, 255, 255, 0.08);
              }

              .tree-node-content {
                display: flex;
                align-items: center;
              }
            }

            &.is-current > .el-tree-node__content {
              background-color: rgba(64, 158, 255, 0.2);
              color: var(--el-color-primary);
            }

            .el-tree-node__expand-icon {
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }

    .resize-handle {
      width: 4px;
      background: transparent;
      cursor: col-resize;
      position: relative;

      &:hover {
        background: rgba(64, 158, 255, 0.3);
      }

      &:active {
        background: var(--el-color-primary);
      }

      &::before {
        content: "";
        position: absolute;
        left: -2px;
        right: -2px;
        top: 0;
        bottom: 0;
        background: transparent;
      }
    }

    .content-section {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-width: 0;

      .search-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        gap: 16px;



        .action-buttons {
          display: flex;
          gap: 8px;
        }
      }
      .search-input {
          width: 300px;
          margin-left: 16px;
          :deep(.el-input__wrapper) {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: none;

            &:hover {
              border-color: rgba(255, 255, 255, 0.2);
            }

            &.is-focus {
              border-color: var(--el-color-primary);
            }
          }

          :deep(.el-input__inner) {
            color: rgba(255, 255, 255, 0.85);

            &::placeholder {
              color: rgba(255, 255, 255, 0.45);
            }
          }

          .search-icon {
            cursor: pointer;
            color: rgba(255, 255, 255, 0.65);

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }

      .document-list-section {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .table-container {
          flex: 1;
          overflow: hidden;
          .sequence-text,
          .document-name-text,
          .updater-text,
          .update-time-text {
            color: rgba(255, 255, 255, 0.85);
            font-size: 13px;
          }

          .operation-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            align-items: center;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
              height: auto;
              min-height: auto;
            }
          }

          :deep(.im-table) {
            .el-table {
              background-color: transparent;
              border: 1px solid rgba(255, 255, 255, 0.1);

              .el-table__header {
                background-color: rgba(255, 255, 255, 0.05);

                th {
                  background-color: transparent;
                  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                  color: rgba(255, 255, 255, 0.85);
                }
              }



              .el-table__fixed-right {
                background-color: rgba(19, 24, 41, 0.95);
              }
            }

            .el-pagination {
              background-color: transparent;
              color: rgba(255, 255, 255, 0.85);

              .el-pager li {
                background-color: transparent;
                color: rgba(255, 255, 255, 0.65);
                border: 1px solid rgba(255, 255, 255, 0.1);

                &:hover {
                  color: var(--el-color-primary);
                  border-color: var(--el-color-primary);
                }

                &.is-active {
                  background-color: var(--el-color-primary);
                  color: #ffffff;
                  border-color: var(--el-color-primary);
                }
              }

              .btn-prev,
              .btn-next {
                background-color: transparent;
                color: rgba(255, 255, 255, 0.65);
                border: 1px solid rgba(255, 255, 255, 0.1);

                &:hover {
                  color: var(--el-color-primary);
                  border-color: var(--el-color-primary);
                }
              }

              .el-select .el-select__wrapper {
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
              }
            }
          }
        }
      }
    }
  }
}
</style>
