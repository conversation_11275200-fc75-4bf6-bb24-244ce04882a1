<template>
  <div class="container">
    <div class="canvas-container">
      <div class="canvas" ref="canvasRef" />
    </div>
    <div class="controls">
      <button @click="zoomIn" title="放大"><i class="bpmn-icon-zoom-in">+</i></button>
      <button @click="zoomOut" title="缩小"><i class="bpmn-icon-zoom-out">-</i></button>
      <button @click="resetView" title="重置视图"><i class="bpmn-icon-fit-to-size">[ ]</i></button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, watch } from 'vue';
import BpmnViewer from 'bpmn-js/lib/NavigatedViewer';
import { http } from '@/utils/http';

import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';

// Props 定义
interface Props {
  processId?: string;
}

const props = defineProps<Props>();

const canvasRef = ref<HTMLDivElement | null>(null);
let viewer: BpmnViewer | null = null;

// 获取流程图数据的接口
const getProcessInfo = async (id: string) => {
  try {
    const response = await http.request<{
      status: string;
      msg: string;
      data: {
        jsonXml: string;
      };
    }>(
      'get',
      `/eam-pm/process/info?id=${id}`
    );
    
    if (response.status === '0' && response.data?.jsonXml) {
      return response.data.jsonXml;
    } else {
      throw new Error(response.msg || '获取流程图数据失败');
    }
  } catch (error) {
    console.error('Failed to fetch process info:', error);
    throw error;
  }
};

// 加载流程图
const loadBpmnDiagram = async (processId: string) => {
  if (!viewer || !processId) return;

  try {
    const xmlData = await getProcessInfo(processId);
    await viewer.importXML(xmlData);
    resetView(); // 初始加载后适应视图
  } catch (error) {
    console.error('Failed to load BPMN XML:', error);
  }
};

// 初始化 BPMN 查看器
const initViewer = () => {
  if (!canvasRef.value) return;

  viewer = new BpmnViewer({
    container: canvasRef.value,
  });
};

// 销毁查看器
const destroyViewer = () => {
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
};

// 缩放控制方法
const zoomIn = () => {
  if (viewer) {
    viewer.get('zoomScroll').stepZoom(1); // 放大一级
  }
};

const zoomOut = () => {
  if (viewer) {
    viewer.get('zoomScroll').stepZoom(-1); // 缩小一级
  }
};

// 重置视图 - 关键方法
const resetView = () => {
  if (viewer) {
    const canvas = viewer.get('canvas');
    
    // 使用 'fit-viewport' 并提供 50px 的内边距
    // 这会让图形在缩放后四周留有空白，从而实现完美的视觉居中
    canvas.zoom('fit-viewport', {
      padding: 50 
    });
  }
};

// 监听 processId 变化
watch(() => props.processId, (newProcessId) => {
  if (newProcessId) {
    loadBpmnDiagram(newProcessId);
  }
}, { immediate: true });

onMounted(() => {
  initViewer();
  
  // 如果有初始的 processId，加载流程图
  if (props.processId) {
    loadBpmnDiagram(props.processId);
  }
});

onBeforeUnmount(() => {
  destroyViewer();
});

// 暴露方法给父组件
defineExpose({
  loadBpmnDiagram,
  zoomIn,
  zoomOut,
  resetView
});
</script>

<style scoped>
.container {
  position: relative;
  height: 600px;
  width: 100%;
}

.canvas-container {
  height: 100%;
  width: 100%;
}

.canvas {
  height: 100%;
  width: 100%;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
}

.controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f8f8;
  border: 1px solid #ccc;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.2s;
}

.controls button:hover {
  background-color: #e0e0e0;
}

.controls i {
  font-size: 20px;
}

/* 移除 bpmn.io logo */
:deep(.bjs-powered-by) {
  display: none;
}
</style>
