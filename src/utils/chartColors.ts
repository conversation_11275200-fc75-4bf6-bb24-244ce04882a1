/**
 * 图表颜色工具类
 * 提供高质量、不重复、视觉友好的颜色生成方案
 */

/**
 * 高质量颜色生成函数
 * 确保颜色明亮、不重复、视觉和谐
 * @param count 需要生成的颜色数量
 * @returns 颜色数组
 */
export const generateDistinctColors = (count: number): string[] => {
  if (count === 0) return [];

  // 扩展的高质量颜色池 - 确保至少30种不重复的颜色，满足20+专业的需求
  const baseColors = [
    // 第一组：暖色系 - 红橙黄
    '#FF6B6B', // 珊瑚红 - 温暖活泼
    '#FF8E53', // 橙红色 - 热情奔放
    '#FFEAA7', // 柠檬黄 - 明亮温暖
    '#F7DC6F', // 金黄色 - 富贵典雅
    '#F8C471', // 橙黄色 - 活力四射
    '#FAD7A0', // 桃色 - 温暖甜美
    '#F1948A', // 浅红色 - 温暖柔和
    '#FFAB91', // 浅橙色 - 温馨舒适

    // 第二组：冷色系 - 蓝绿青
    '#4ECDC4', // 青绿色 - 清新自然
    '#45B7D1', // 天蓝色 - 清澈明亮
    '#85C1E9', // 浅蓝色 - 宁静清澈
    '#AED6F1', // 天空蓝 - 广阔深邃
    '#98D8C8', // 薄荷蓝 - 清新舒缓
    '#A3E4D7', // 水绿色 - 清透自然
    '#81C784', // 清新绿 - 自然生机
    '#4DD0E1', // 青蓝色 - 清凉透彻

    // 第三组：中性色系 - 绿紫粉
    '#96CEB4', // 薄荷绿 - 清新淡雅
    '#82E0AA', // 浅绿色 - 生机盎然
    '#A9DFBF', // 嫩绿色 - 生机勃勃
    '#ABEBC6', // 翠绿色 - 清新淡雅
    '#DDA0DD', // 梅花紫 - 优雅神秘
    '#BB8FCE', // 淡紫色 - 浪漫温柔
    '#D7BDE2', // 薰衣草紫 - 优雅迷人
    '#CE93D8', // 紫罗兰 - 神秘优雅

    // 第四组：特殊色系 - 确保足够的区分度
    '#F9E79F', // 浅黄色 - 明亮温和
    '#DCEDC1', // 淡绿黄 - 清新自然
    '#FFE082', // 亮黄色 - 阳光明媚
    '#FFCC80', // 浅橙黄 - 温暖柔和
    '#BCAAA4', // 暖灰色 - 稳重大方
    '#B39DDB'  // 淡蓝紫 - 优雅宁静
  ];

  // 如果需要的颜色数量少于等于基础颜色池，直接使用并打乱顺序
  if (count <= baseColors.length) {
    const shuffledColors = [...baseColors].sort(() => Math.random() - 0.5);
    return shuffledColors.slice(0, count);
  }

  // 如果需要更多颜色，先使用基础颜色池，然后用优化算法生成额外颜色
  const colors: string[] = [];

  // 先添加打乱后的基础颜色
  const shuffledBaseColors = [...baseColors].sort(() => Math.random() - 0.5);
  colors.push(...shuffledBaseColors);

  // 如果还需要更多颜色，使用改进的HSL算法生成
  if (count > baseColors.length) {
    const additionalCount = count - baseColors.length;
    const goldenRatio = 0.618033988749895; // 黄金比例

    // 使用更精确的色相分割，确保与现有颜色有足够区分度
    const hueStep = 360 / additionalCount; // 平均分割色相环
    let startHue = Math.random() * 360; // 随机起始点

    for (let i = 0; i < additionalCount; i++) {
      // 计算当前色相，确保均匀分布
      const hue = (startHue + (i * hueStep * goldenRatio)) % 360;

      // 使用多层次的饱和度和亮度，增加变化
      const saturationLevels = [0.65, 0.75, 0.85]; // 三个饱和度层次
      const lightnessLevels = [0.55, 0.65, 0.75];  // 三个亮度层次

      const saturation = saturationLevels[i % saturationLevels.length];
      const lightness = lightnessLevels[Math.floor(i / saturationLevels.length) % lightnessLevels.length];

      // 转换为HSL颜色
      const color = hslToHex(hue, saturation * 100, lightness * 100);
      colors.push(color);
    }
  }

  return colors.slice(0, count);
};

/**
 * HSL转HEX颜色函数
 * @param h 色相 (0-360)
 * @param s 饱和度 (0-100)
 * @param l 亮度 (0-100)
 * @returns HEX颜色值
 */
export const hslToHex = (h: number, s: number, l: number): string => {
  l /= 100;
  const a = s * Math.min(l, 1 - l) / 100;
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color).toString(16).padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

/**
 * 生成渐变色
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param steps 渐变步数
 * @returns 渐变色数组
 */
export const generateGradientColors = (startColor: string, endColor: string, steps: number): string[] => {
  const colors: string[] = [];

  // 解析起始和结束颜色
  const start = hexToRgb(startColor);
  const end = hexToRgb(endColor);

  if (!start || !end) return [];

  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1);
    const r = Math.round(start.r + (end.r - start.r) * ratio);
    const g = Math.round(start.g + (end.g - start.g) * ratio);
    const b = Math.round(start.b + (end.b - start.b) * ratio);

    colors.push(rgbToHex(r, g, b));
  }

  return colors;
};

/**
 * HEX转RGB
 * @param hex HEX颜色值
 * @returns RGB对象
 */
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/**
 * RGB转HEX
 * @param r 红色值 (0-255)
 * @param g 绿色值 (0-255)
 * @param b 蓝色值 (0-255)
 * @returns HEX颜色值
 */
export const rgbToHex = (r: number, g: number, b: number): string => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};

/**
 * 预定义的主题色彩方案
 */
export const themeColors = {
  // 商务主题 - 专业稳重
  business: ['#1890FF', '#52C41A', '#FAAD14', '#F5222D', '#722ED1', '#13C2C2'],

  // 活力主题 - 年轻活泼
  vibrant: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],

  // 自然主题 - 清新自然
  nature: ['#52C41A', '#13C2C2', '#1890FF', '#FAAD14', '#722ED1', '#F5222D'],

  // 温暖主题 - 温馨舒适
  warm: ['#FF7875', '#FFA940', '#FFEC3D', '#73D13D', '#40A9FF', '#B37FEB'],

  // 冷色主题 - 冷静理性
  cool: ['#1890FF', '#13C2C2', '#722ED1', '#52C41A', '#FAAD14', '#F5222D']
};

/**
 * 根据主题获取颜色
 * @param theme 主题名称
 * @param count 需要的颜色数量
 * @returns 颜色数组
 */
export const getThemeColors = (theme: keyof typeof themeColors, count: number): string[] => {
  const colors = themeColors[theme] || themeColors.business;

  if (count <= colors.length) {
    return colors.slice(0, count);
  }

  // 如果需要更多颜色，补充生成
  const additionalColors = generateDistinctColors(count - colors.length);
  return [...colors, ...additionalColors];
};

/**
 * 专门为大量数据（20+项）优化的颜色生成函数
 * 使用更精确的算法确保每种颜色都有明显区分度
 * @param count 需要的颜色数量
 * @returns 高区分度颜色数组
 */
export const generateHighContrastColors = (count: number): string[] => {
  if (count === 0) return [];

  const colors: string[] = [];

  // 使用更科学的色相分割方法
  const hueSegments = Math.max(count, 20); // 至少分割20段
  const hueStep = 360 / hueSegments;

  // 定义多个饱和度和亮度层次，确保即使色相相近也能区分
  const saturationLevels = [0.7, 0.85, 0.6, 0.9, 0.75]; // 5个饱和度层次
  const lightnessLevels = [0.6, 0.7, 0.55, 0.75, 0.65];  // 5个亮度层次

  // 随机起始色相，每次生成不同的颜色组合
  const startHue = Math.random() * 360;

  for (let i = 0; i < count; i++) {
    // 计算色相，使用黄金比例微调确保更好的分布
    const baseHue = (startHue + (i * hueStep)) % 360;
    const hue = (baseHue + (i * 137.5)) % 360; // 137.5° 是黄金角度

    // 根据索引选择不同的饱和度和亮度层次
    const saturation = saturationLevels[i % saturationLevels.length];
    const lightness = lightnessLevels[Math.floor(i / saturationLevels.length) % lightnessLevels.length];

    // 为相邻颜色添加微调，进一步增加区分度
    const hueAdjustment = (i % 3) * 15 - 15; // -15, 0, 15 度的微调
    const finalHue = (hue + hueAdjustment + 360) % 360;

    const color = hslToHex(finalHue, saturation * 100, lightness * 100);
    colors.push(color);
  }

  return colors;
};

/**
 * 测试颜色生成效果的辅助函数
 * @param count 要生成的颜色数量
 * @returns 包含颜色信息的测试结果
 */
export const testColorGeneration = (count: number) => {
  console.log(`🎨 测试生成 ${count} 种颜色:`);

  const standardColors = generateDistinctColors(count);
  const highContrastColors = generateHighContrastColors(count);

  console.log('标准颜色生成:', standardColors);
  console.log('高对比度颜色生成:', highContrastColors);

  // 计算颜色差异度
  const calculateColorDifference = (colors: string[]) => {
    let minDifference = Infinity;
    for (let i = 0; i < colors.length; i++) {
      for (let j = i + 1; j < colors.length; j++) {
        const diff = getColorDifference(colors[i], colors[j]);
        if (diff < minDifference) {
          minDifference = diff;
        }
      }
    }
    return minDifference;
  };

  const standardMinDiff = calculateColorDifference(standardColors);
  const highContrastMinDiff = calculateColorDifference(highContrastColors);

  console.log(`标准算法最小色差: ${standardMinDiff.toFixed(2)}`);
  console.log(`高对比度算法最小色差: ${highContrastMinDiff.toFixed(2)}`);

  return {
    standardColors,
    highContrastColors,
    standardMinDiff,
    highContrastMinDiff
  };
};

/**
 * 计算两个颜色之间的差异度（简化版）
 * @param color1 颜色1
 * @param color2 颜色2
 * @returns 差异度数值
 */
const getColorDifference = (color1: string, color2: string): number => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);

  if (!rgb1 || !rgb2) return 0;

  // 使用欧几里得距离计算RGB差异
  const rDiff = rgb1.r - rgb2.r;
  const gDiff = rgb1.g - rgb2.g;
  const bDiff = rgb1.b - rgb2.b;

  return Math.sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff);
};