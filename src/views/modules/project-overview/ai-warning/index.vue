
<template>
  <div class="echarts-ai-header-da"></div>
  <div class="dates">
    <div id="today" class="today active-today" @click="wsAiToday"></div>
    <div id="all" class="all" @click="wsAiAll"></div>
  </div>
  <div class="charts-ai-box">
    <div class="ai-img"></div>
    <div class="echarts-pies" ref="echarts-pies" id="ai-warn"></div>
    <div class="total-box">
      <div class="text-number">  {{ state.allCount }}</div>

      <div>总数</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from "echarts"
import { onMounted, onBeforeUnmount, watch, reactive, ref } from 'vue'
import moment from 'moment'
import Socket from '@/websocket'
import store from '@/store'
import 'animate.css'

const state = reactive({
  projectId: '',
  aiList: [],
  allCount: '0'
})
const wsListData = reactive({
  list: []
})
let myChart = null
let webSocket = null

let dateFlag = ref('0')

onMounted(() => {
  state.allCount = '0';
  state.aiList = [];
  const provinceKey = store.useProjectInfoStore.provinceKey
  if (provinceKey != '') {
    if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
      state.projectId = ""
      store.useProjectInfoStore.displayData[provinceKey].forEach(element => {
        state.projectId = state.projectId + element.projectId + ","
      });
      wsAiMessage(state.projectId)
    }
  }
  renderCharts()
})
// onBeforeUnmount(() => {
//   if (webSocket) {
//     webSocket.destroy()
//   }
// }),
// 监控项目id,获取AI数据
watch(() => store.useProjectInfoStore.provinceKey, (newval, oldval) => {
  const provinceKey = store.useProjectInfoStore.provinceKey
  if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
    state.projectId = ""
    store.useProjectInfoStore.displayData[provinceKey].forEach(element => {
      state.projectId = state.projectId + element.projectId + ","
    });
    wsAiMessage(state.projectId)
  } else {
    if (webSocket) {
      webSocket.destroy()
    }
    state.projectId = ""
    state.aiList = []
    state.allCount = '0'
    myChart.setOption({ series: { data: state.aiList } })
  }
}, { deep: true });
const renderCharts = () => {
  var chartDom = document.getElementById('ai-warn') as HTMLElement;
  myChart = echarts.getInstanceByDom(chartDom)
  if (myChart == null) {
    myChart = echarts.init(chartDom);
  }
  // var myChart = echarts.init(chartDom);
  var option;
  option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: "#182137", //设置背景颜色
      textStyle: {
        color: "white" //设置文字颜色
      },
      borderColor: "#2D8CF0", //设置边框颜色
      // formatter: "{b} : {c}%" // 自定义图表中提示格式
    },
    legend: {
      icon: 'circle',
      orient: 'vertical',
      type: 'scroll', // 设置图例翻页
      scrollDataIndex: 0,  // 设置需要滚动的系列的数据索引
      height: '170px',
      top: '15%',
      right: '10%',
      itemWidth: 10,   // 设置图例图形的宽
      itemHeight: 10,  // 设置图例图形的高
      textStyle: { //圖例文字的樣式
        color: '#6989A4',
        fontSize: 14
      },
      // itemGap设置各个item之间的间隔，单位px，默认为10，横向布局时为水平间隔，纵向布局时为纵向间隔
      itemGap: 16,
      selectedMode: false,
      formatter: function (name: string) { //图例后添加数值
        let data = state.aiList;
        let tarValue;
        for (let i = 0; i < data.length; i++) {
          if (data[i].name === name) {
            tarValue = data[i].value
          }
        }
        return name + "  " + tarValue + "%";
      }
    },
    series: [
      {
        // name: 'Access From',
        type: 'pie',
        radius: ['55%', '69s%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 15,
            shadowColor: "rgba(91, 187, 255, 0.8)",
          },
        },
        labelLine: {
          show: false
        },
        center: ['24.9%', '49.5%'],
        data: state.aiList,
        universalTransition: true,
        animationDurationUpdate: 500,
        animationEasingUpdate: "cubicIn",
        animationDelayUpdate: function (idx: number) {
          return idx * 100;
        },
      },
    ]
  };

  option && myChart.setOption(option);
};

/**
 * 点击今日按钮
 */
const wsAiToday = () => {
  dateFlag.value = '0' // 代表今日
  // 动态设置样式
  document.getElementById('today').classList.add('active-today')
  document.getElementById('all').classList.remove('active-all')

  // 添加动画,动画结束后移除
  const element = document.querySelector('.charts-ai-box');
  element.classList.add('animate__animated', 'animate__flipOutY');
  element.addEventListener('animationend', () => {
    element.classList.remove('animate__animated', 'animate__flipOutY')
  });
  wsAiMessage(state.projectId)
}

/**
 * 点击全部按钮
 */
const wsAiAll = () => {
  dateFlag.value = '1' // 代表全部
    // 动态设置样式
  document.getElementById('all').classList.add('active-all')
  document.getElementById('today').classList.remove('active-today')
  // 添加动画,动画结束后移除
  const element = document.querySelector('.charts-ai-box');
  element.classList.add('animate__animated', 'animate__flipInY');
  element.addEventListener('animationend', () => {
    element.classList.remove('animate__animated', 'animate__flipInY')
  });
  wsAiMessage(state.projectId)
}

const wsAiMessage = (projectId: string) => {
  if (webSocket) {
    webSocket.destroy()
  }
  let urlType = ''
  if (dateFlag.value == '1') {
    urlType = '*'
  } else {
    urlType = moment(new Date()).format('YYYY-MM-DD')
  }
  webSocket = new Socket({ url: import.meta.env.VITE_BASE_AI + projectId + '/1/' + urlType })
  webSocket.onmessage((data: any) => {
    // wsListData.list = data
    state.aiList = []
    if (data && data.length > 0) {
      data.forEach(item => {
        if (item.eventName && item.aiPercent) {
          state.allCount = item.allCount
          state.aiList.push({ "name": item.eventName, "value": item.aiPercent })
        }
      });
    } else {
      state.allCount = '0'
    }
    myChart.setOption({ series: { data: state.aiList } });
  })
  if (dateFlag.value == '0') {
    webSocket.options.heartMsg = { projectId: projectId, platform: "1", eventTime: moment(new Date()).format('YYYY-MM-DD') }
  }
  if (dateFlag.value == '1') {
    webSocket.options.heartMsg = { projectId: projectId, platform: "1", eventTime: "*" }
  }
}

/**
 * AI告警数字--过滤
 * @param params 
 */
const filterNumber = (total: number) => {
  
}

</script>
<style lang="scss" scoped>
.echarts-ai-header-da {
  width: 100%;
  height: 50px;
  background: url(../../../assets/img/ai_header_ta.png) no-repeat center center;
  background-size: cover;
}

.dates {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  width: 98%;
  height: 28px;

  .today {
    width: 64px;
    height: 100%;
    background: url(../../../assets/img/ai_today_no.png) no-repeat;
  }

  .active-today {
    background: url(../../../assets/img/ai_today_active.png) no-repeat !important;
  }

  .active-all {
    background: url(../../../assets/img/ai_all_active.png) no-repeat !important;
  }

  .all {
    width: 63px;
    height: 100%;
    background: url(../../../assets/img/ai_all_no.png) no-repeat;
  }
}

.charts-ai-box {
  width: 100%;
  height: 100%;
  position: relative;

  .ai-img {
    position: absolute;
    background: url(../../../assets/img/round-ai.png);
    width: 168px;
    height: 168px;
    left: calc(6%);
    top: calc(5%);
    background-repeat: no-repeat;
    background-size: cover;
  }

  .echarts-pies {
    position: absolute;
    width: 100%;
    height: 200px;
  }

  .total-box {
    width: 116px;
    height: 116px;
    position: absolute;
    left: calc(12%);
    top: calc(15%);
    font-size: 16rpx;
    font-weight: bold;
    color: #517CBE;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .text-number {
      font-size: 24px;
      font-family: Regular;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 24px;
      margin-bottom: 9px;
      font-weight: bold
    }
  }

}
</style>
