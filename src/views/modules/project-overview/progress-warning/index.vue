<template>
	<div class="container">
		<div class="head">
			<div class="large-imgBox">
				<span class="more">更多</span>
				<div
					class="large-img"
					@click="showBigInfo"></div>
			</div>
		</div>
		<div class="pro-parent">
			<div class="all-pro">
				<div class="all-img"></div>
				<span class="all-text">全部项目</span>
				<span class="all-text-data">{{ state.provinceKeycount?.total }}</span>
			</div>
			<div class="delay-pro">
				<div class="delay-img"></div>
				<span class="delay-text">延期项目</span>
				<span class="delay-text-data">{{ state.provinceKeycount?.delay }}</span>
			</div>
		</div>
		<el-scrollbar class="list-scroll">
			<div
				v-for="(item, index) in state.list"
				:key="index">
				<div
					class="list-parent"
					@click="showListInfo(item)">
					<div class="list-index">
						<div>
							<div class="list-img-id">{{ index + 1 }}</div>
						</div>
						<span
							class="list-name"
							:title="item.projectName"
							>{{ item.shortName }}</span
						>
						<!-- <div v-if="item?.delayLevel == '0'" class="state-narmal"></div> -->
						<div
							v-if="item?.delayLevel == '1'"
							class="state-delay"></div>
						<div
							v-if="item?.delayLevel == '2'"
							class="state-s-dealy"></div>
						<!-- <div class="state-text">{{ item?.currentStage }}</div> -->
					</div>
					<el-tooltip
						v-if="item"
						effect="customized"
						:content="
							item.delayDays == 0
								? '计划开始日期:' +
								  item.startDate?.substring(0, 10) +
								  '   计划竣工日期:' +
								  item.endDate?.substring(0, 10)
								: '计划开始日期:' +
								  item.startDate?.substring(0, 10) +
								  '   计划竣工日期:' +
								  item.endDate?.substring(0, 10) +
								  '   延期天数:' +
								  item.delayDays
						"
						placement="top-end">
						<div class="list-progress">
							<!-- <div class="demo-progress">
                  <el-progress :percentage="Number(item.buildPercent)" :show-text="false" :color="customColors" />
                </div> -->
							<div class="scheduleCont_1_left_1">
								<div
									:style="{ width: getwidth(index, 0) }"
									class="un_xg_1_3_1"></div>
								<div
									:style="{ width: getwidth(index, 1) }"
									class="un_xg_1_3_2"></div>
								<div
									:style="{ width: getwidth(index, 2) }"
									class="un_xg_1_3_3"></div>
								<div
									:style="{ width: getwidth(index, 3) }"
									class="un_xg_1_3_4"></div>
								<div
									:style="{ width: getwidth(index, 4) }"
									class="un_xg_1_3_5"></div>
								<div
									:style="{ width: getwidth(index, 5) }"
									class="un_xg_1_3_6"></div>
								<div
									:style="{ width: getwidth(index, 6) }"
									class="un_xg_1_3_7"></div>
							</div>
						</div>
					</el-tooltip>
				</div>
			</div>
		</el-scrollbar>
	</div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import store from '@/store'

const emit = defineEmits(['showBigInfo,showListInfo'])

const state = reactive({
	list: [],
	provinceKeycount: store.useProjectInfoStore.provinceKey,
})
onMounted(() => {
	const provinceKey = store.useProjectInfoStore.provinceKey || localStorage.getItem('provinceKey')
	console.log('子项目追踪-provinceKey:', provinceKey)
	let displayData = localStorage.getItem('displayData') || ''
	console.log('子项目追踪displayData:', displayData)
	setTimeout(() => {
		if (displayData) {
			displayData = JSON.parse(displayData)
			if (provinceKey != '') {
				let isHas =
					store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey) || displayData.hasOwnProperty(provinceKey)
				if (isHas) {
					state.list = store.useProjectInfoStore.displayData[provinceKey] || displayData
					console.log('mounted-state.list:', state.list)
					state.provinceKeycount = store.useProjectInfoStore.displayData[provinceKey + 'count']
				}
			}
		}
	}, 20)
})
watch(
	() => store.useProjectInfoStore.provinceKey,
	(_newValue, _oldValue) => {
		const provinceKey = store.useProjectInfoStore.provinceKey || localStorage.getItem('provinceKey')
		console.log('子项目追踪222-watch-provinceKey:', provinceKey)
		let displayData = localStorage.getItem('displayData') || ''
		console.log('子项目追踪222-watch-displayData:', displayData)
		if (displayData) {
			displayData = JSON.parse(displayData)
			if (
				store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey) ||
				displayData.hasOwnProperty(provinceKey)
			) {
				state.list = store.useProjectInfoStore.displayData[provinceKey] || displayData
				console.log('watch222-state.list:', state.list)
				state.provinceKeycount = store.useProjectInfoStore.displayData[provinceKey + 'count']
			} else {
				state.list = []
				state.provinceKeycount = []
			}
		}
	},
	{ deep: true, flush: 'post' }
)

const getwidth = (index: any, val: any) => {
	let width = 0 + '%'
	if (state.list[index].stageList != null) {
		if (state.list[index].stageList.length != 0) {
			width = state.list[index].stageList[val]?.stagePercent + '%'
		}
	}
	return width
}

// TODO:待UI提供后，显示放大的图片；点击次放大按钮，回调父组件方法，显示放大后的页面
const showBigInfo = () => {
	emit('showBigInfo', true)
}

// TODO:列表点击，回调父组件方法，显示项目详情
const showListInfo = (item: any) => {
	emit('showListInfo', item)
}
</script>
<style>
.el-popper.is-customized {
	/* Set padding to ensure the height is 32px */
	padding: 6px 12px;
	background: #165786;
	color: #bdd8ee;
}

.el-popper.is-customized .el-popper__arrow::before {
	background: #165786;
	right: 0;
}
</style>
<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.head {
		position: relative;
		width: 100%;
		height: 54px;
		background: url(../../../assets/img/<EMAIL>) no-repeat;
		background-size: contain;
		.large-imgBox {
			display: flex;
			align-items: center;
			position: absolute;
			left: 280px;
			top: 16px;
			.more {
				display: inline-block;
				height: 18px;
				margin-right: 8px;
			}
			.large-img {
				display: inline-block;
				width: 18px;
				height: 18px;
				background: url(../../../assets/img/<EMAIL>) no-repeat;
				background-size: cover;
			}
		}
	}

	.pro-parent {
		margin-top: 26px;
		widows: 100%;
		height: 70px;
		display: flex;
		justify-content: center;
		align-items: center;

		.all-pro {
			display: flex;
			margin-right: 30.5px;
			background: url(../../../assets/img/prosd_title_bg.png);
			width: 190px;
			justify-content: center;
			align-items: center;
			height: 100%;

			.all-img {
				width: 20px;
				height: 20px;
				background: url(../../../assets/img/pro_all_logo.png);
			}

			.all-text {
				margin-left: 16px;
				color: rgba(255, 255, 255, 0.6);
				font-size: 16px;
			}

			.all-text-data {
				margin-left: 16px;
				font-size: 24px;
				font-family: Regular;
				font-weight: 400;
				color: #168ad1;
				line-height: 24px;
			}
		}

		.delay-pro {
			display: flex;
			margin-left: 30.5px;
			background: url(../../../assets/img/prosd_title_bg.png);
			width: 190px;
			height: 100%;
			align-items: center;
			justify-content: center;

			.delay-img {
				width: 20px;
				height: 20px;
				background: url(../../../assets/img/pro_delay_bg.png);
			}

			.delay-text {
				margin-left: 16px;
				color: rgba(255, 255, 255, 0.6);
				font-size: 16px;
			}

			.delay-text-data {
				margin-left: 16px;
				font-size: 24px;
				font-family: Regular;
				font-weight: 400;
				color: #ff8080;
				line-height: 24px;
			}
		}
	}

	.list-scroll {
		width: 100%;
		height: 450px;
		padding: 0 32px;
		display: flex;
		flex-direction: column;
		padding-bottom: 20px;
		// justify-content: space-evenly;
		margin-top: 10px;

		.list-parent {
			width: 100%;
			// height: 72px;
			display: flex;
			margin-top: 20px;
			flex-direction: column;
			position: relative;

			.list-index {
				width: 100%;
				display: flex;
				margin-top: 10px;

				.list-img-id {
					align-self: flex-start;
					display: flex;
					align-items: center;
					justify-content: center;
					// margin-top: 25px;
					width: 24px;
					height: 24px;
					background: url(../../../assets/img/pro_item_logo.png);
				}

				.list-name {
					width: 400px; //设置宽度
					overflow: hidden; //溢出隐藏
					text-overflow: ellipsis; //属性值表示当对象内文本溢出时显示省略标记，省略标记插入的位置是最后一个字符。
					white-space: nowrap; //只保留一个空白，文本不会换行，会在在同一行上继续，直到遇到br标签为止。
					align-self: flex-start;
					margin-top: 5px;
					margin-left: 14px;
					font-size: 16px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #d3d6dd;
					line-height: 16px;
				}

				.state-narmal {
					position: absolute;
					width: 14px;
					height: 14px;
					right: 60px;
					background: url(../../../assets/img/schedule-completed-bg.png) no-repeat;
					background-size: contain;
				}

				.state-delay {
					position: absolute;
					width: 14px;
					height: 14px;
					right: 60px;
					background: url(../../../assets/img/general-extension-bg.png) no-repeat;
					background-size: contain;
				}

				.state-s-dealy {
					position: absolute;
					width: 14px;
					height: 14px;
					right: 60px;
					background: url(../../../assets/img/serious-delay-bg.png) no-repeat;
					background-size: contain;
				}

				.state-text {
					position: absolute;
					right: 0px;
					color: #7c8cb2;
					font-size: 14px;
					font-weight: 400;
					line-height: 14px;
				}
			}

			.list-progress {
				position: relative;
				display: flex;
				margin-top: 11px;
				height: 6px;
				align-items: center;
				background: #252e44;
				width: 100%;

				.scheduleCont_1_left_1 {
					position: absolute;
					width: 100%;
					height: 6px;
					border-radius: 25px;
					display: flex;
					align-items: center;
					animation: donghua 1.5s;

					.un_xg_1_3_1 {
						height: 6px;
						background: #1559ff;
					}

					.un_xg_1_3_2 {
						height: 6px;
						background: #46970e;
					}

					.un_xg_1_3_3 {
						height: 6px;
						background: #ff7926;
					}

					.un_xg_1_3_4 {
						height: 6px;
						background: #00d3ff;
					}

					.un_xg_1_3_5 {
						height: 6px;
						background: #59b4ff;
					}

					.un_xg_1_3_6 {
						height: 6px;
						background: #74e870;
					}

					.un_xg_1_3_7 {
						height: 6px;
						background: #ffcd43;
					}
				}

				@keyframes donghua {
					0% {
						opacity: 1;
						width: 0;
					}

					100% {
						opacity: 1;
						width: 100%;
					}
				}

				// .list-data {
				//   font-size: 18px;
				//   font-family: DIN-BoldItalic-Regular, DIN-BoldItalic;
				//   font-weight: 500;
				//   color: #D3E2FF;
				//   line-height: 18px;
				//   -webkit-background-clip: text;
				//   margin-left: 14px;

				// }

				// .demo-progress {
				//   position: absolute;
				//   width: 100%;
				//   height: 6px;

				//   /* 渐变进度条 */
				//   :deep(.el-progress-bar__inner) {
				//     height: 6px;
				//     background-color: unset;

				//   }

				//   :deep(.el-progress-bar__outer) {
				//     height: 6px;
				//     background-color: #252E44;
				//   }
				// }
			}
		}
	}
}
</style>
