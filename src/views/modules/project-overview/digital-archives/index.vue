
<template>
  <div class="echarts-da-header"></div>
  <div class="today">
    <img class="logo" src="../../../assets/img/today_add.png" />
    <img class="fonts" src="../../../assets/img/font_add.png" />
    <div class="line"></div>
    <span class="typeface">{{ state.todayCount }}</span>
  </div>
  <div class="charts-box">
    <div class="echarts-pie" ref="echarts-pie" :id="id" :aiList="list"></div>
    <div class="total-box">
      <div class="text-number">{{ state.allCount }}</div>
      <div>总数</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from "echarts";
import { onMounted, watch, reactive } from "vue";
import store from "@/store";
import { queryDaCollectCountApi } from "@/script/api/common/commomApi";

const state = reactive({
  aiList: [],
  allCount: 0,
  todayCount: 0,
});

let myChart: any = null;

onMounted(() => {
  state.allCount = 0;
  state.aiList = [];
  let projectId = "";
  const provinceKey = store.useProjectInfoStore.provinceKey;
  console.log(
    "store.useProjectInfoStore.displayData: ",
    store.useProjectInfoStore.displayData
  );
  if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
    store.useProjectInfoStore.displayData[provinceKey].forEach((element) => {
      projectId += element.projectId + ",";
    });
    queryDaCollectCount(projectId);
  }

  renderChart();
});

const props = withDefaults(
  defineProps<{
    id?: string;
    list?: Array<T>;
  }>(),
  {
    id: "echarts-pie",
  }
);
watch(
  () => props.list,
  (newval, oldval) => {
    if (newval != null) {
      if (newval.length > 0) {
        state.allCount = newval[0].allCount;

        //newval就是最新更新的result。
        state.aiList = [];
        for (let i = 0; i < newval.length; i++) {
          const resData = newval[i];
          if (resData.eventName && resData.aiPercent) {
            state.aiList[i] = {
              name: resData.eventName,
              value: resData.aiPercent,
            };
          }
        }
      } else {
        state.allCount = 0;
        state.aiList = [];
        state.todayCount = 0;
      }
    } else {
      state.allCount = 0;
      state.aiList = [];
      state.todayCount = 0;
    }
    renderChart();
  }
);

// 监控key值,获取数字档案数据
watch(
  () => store.useProjectInfoStore.displayData,
  (newval, oldval) => {
    let projectId = "";
    const provinceKey = store.useProjectInfoStore.provinceKey;
    console.log(
      "store.useProjectInfoStore.displayData: ",
      store.useProjectInfoStore.displayData
    );
    if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
      store.useProjectInfoStore.displayData[provinceKey].forEach((element) => {
        projectId += element.projectId + ",";
      });
      queryDaCollectCount(projectId);
    } else {
      projectId = "";
      state.aiList = [];
      state.allCount = 0;
      state.todayCount = 0;
      myChart.setOption({ series: { data: state.aiList } });
    }
  },
  { deep: true }
);

const renderChart = () => {
  var chartDom = document.getElementById(props.id) as HTMLElement;
  myChart = echarts.getInstanceByDom(chartDom);
  if (myChart == null) {
    myChart = echarts.init(chartDom);
  }
  // var myChart = echarts.init(chartDom);
  var option;

  option = {
    tooltip: {
      trigger: "item",
      backgroundColor: "#182137", //设置背景颜色
      textStyle: {
        color: "white", //设置文字颜色
      },
      borderColor: "#2D8CF0", //设置边框颜色
    },
    legend: {
      icon: "circle",
      orient: "vertical",
      type: "scroll", // 设置图例翻页
      scrollDataIndex: 0, // 设置需要滚动的系列的数据索引
      height: "170px",
      top: "6%",
      right: "10%",
      itemWidth: 10, // 设置图例图形的宽
      itemHeight: 10, // 设置图例图形的高
      textStyle: {
        //圖例文字的樣式
        color: "#6989A4",
        fontSize: 14,
      },
      // itemGap设置各个item之间的间隔，单位px，默认为10，横向布局时为水平间隔，纵向布局时为纵向间隔
      itemGap: 16,
      selectedMode: false,
      formatter: function (name: string) {
        //图例后添加数值
        let data = state.aiList;
        let tarValue;
        for (let i = 0; i < data.length; i++) {
          if (data[i].name === name) {
            tarValue = data[i].value;
          }
        }
        return name + "  " + tarValue + "%";
      },
    },
    // animation: true,
    series: [
      {
        // name: 'Access From',
        type: "pie",
        radius: ["70%", "84%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: "bold",
          },
          itemStyle: {
            shadowBlur: 15,
            shadowColor: "rgba(91, 187, 255, 0.8)",
          },
        },
        labelLine: {
          show: false,
        },
        center: ["25%", "46.8%"],
        data: state.aiList,
        universalTransition: true,
        animationDurationUpdate: 500,
        animationEasingUpdate: "cubicIn",
        animationDelayUpdate: function (idx: number) {
          return idx * 100;
        },
      },
    ],
  };
  myChart.clear();
  option && myChart.setOption(option);
};

/**
 * 获取数字档案信息
 * @param projectId
 */
const queryDaCollectCount = async (projectId: string) => {
  const params = {
    region: store.useProjectInfoStore.titleKey,
  };
  const { data } = await queryDaCollectCountApi(params);
  console.log("data:11111111111111111111 ", data);

  state.aiList = [];
  if (data.fileVOS && data.fileVOS.length > 0) {
    state.todayCount = data.updateCount;
    state.allCount = data.total;
    data.fileVOS.forEach((item) => {
      if (item.directoryName && item.percentage) {
       
        state.aiList.push({
          name: item.directoryName,
          value: item.percentage,
        });
      }
    });

  } else {
    state.allCount = 0;
  }
  myChart.setOption({ series: { data: state.aiList } });
};
</script>
<style lang="scss" scoped>
.echarts-da-header {
  width: 100%;
  height: 50px;
  background: url(../../../assets/img/da_header.png) no-repeat;
}

.today {
  margin: 15px auto;
  width: 386px;
  height: 36px;
  background: #202e50;
  opacity: 1;
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  .logo {
    width: 16px;
    height: 16px;
  }

  .fonts {
  }

  .line {
    width: 180px;
    height: 1px;
    background: #2a3e66;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }

  .typeface {
    font-size: 20px;
    color: #fff;
    font-family: Regular;
  }
}

.charts-box {
  width: 100%;
  height: 100%;
  position: relative;

  .echarts-pie {
    position: absolute;
    width: 100%;
    height: 183px;
  }

  .total-box {
    width: 117px;
    height: 117px;
    position: absolute;
    left: calc(11.8%);
    top: calc(9%);
    font-size: 16rpx;
    font-weight: bold;
    color: #517cbe;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: url(../../../assets/img/round-labor-services.png);
    background-repeat: no-repeat;
    background-size: cover;

    .text-number {
      font-size: 24px;
      font-family: Regular;
      font-weight: 500;
      color: #ffffff;
      line-height: 24px;
      margin-bottom: 9px;
      font-weight: bold;
    }
  }
}
</style>
