<template>
  <div class="container">
    <div class="top">
      <div id="1" class="button1 active" @click.self="clickItem">全部项目<span class="span1">{{ state.allNumber }}</span>
      </div>
      <div id="2" class="button2" @click.self="clickItem">延期项目<span class="span2">{{ state.delayNumber }}</span></div>
      <div id="3" class="button3" @click.self="clickItem">全国重点项目<span class="span3">{{ state.mainNumber }}</span></div>
      <div id="4" class="button4" @click.self="clickItem">未开工项目<span class="span4">{{ state.noStartNumber }}</span></div>
      <div id="5" class="button5" @click.self="clickItem">已开工项目<span class="span5">{{ state.buildingNumber }}</span></div>
      <div id="6" class="button6" @click.self="clickItem">已纳管项目<span class="span6">{{ state.accessNumber }}</span></div>
    </div>

    <el-scrollbar class="content-all">
      <div class="content-box" v-for="(value, key) in state.list">
        <div class="title-box">
          <div class="title">{{ key }}
            <div class="color"></div>
          </div>
        </div>
        <div class="list-box">
          <div v-for="(item,index) in value" class="content-item" :key="index">
            <div class="list-index">
              <span class="list-name" @click="showListInfo(item)">{{ item.shortName }}</span>
              <div v-if="item?.delayLevel == '1'" class="state-delay"></div>
              <div v-if="item?.delayLevel == '2'" class="state-s-dealy"></div>

              <div v-if="item?.collect == '0'" class="collect" @click="iscollect(item)"></div>
              <div v-if="item?.collect == '1'" class="collect-no" @click="isNocollect(item)"></div>

            </div>
            <el-tooltip effect="customized"
              :content="item.delayDays == 0 ? '计划开始日期:' + item.startDate.substring(0, 10) + '   计划竣工日期:' + item.endDate.substring(0, 10) : '计划开始日期:' + item.startDate.substring(0, 10) + '   计划竣工日期:' + item.endDate.substring(0, 10) + '   延期天数:' + item.delayDays"
              placement="top-end">
              <div class="list-progress" @click="showListInfo(item)">
                <div class="scheduleCont_1_left_1">
                  <div :style="{ width: getwidth(index, value, 0) }" class="un_xg_1_3_1"></div>
                  <div :style="{ width: getwidth(index, value, 1) }" class="un_xg_1_3_2"></div>
                  <div :style="{ width: getwidth(index, value, 2) }" class="un_xg_1_3_3"></div>
                  <div :style="{ width: getwidth(index, value, 3) }" class="un_xg_1_3_4"></div>
                  <div :style="{ width: getwidth(index, value, 4) }" class="un_xg_1_3_5"></div>
                  <div :style="{ width: getwidth(index, value, 5) }" class="un_xg_1_3_6"></div>
                  <div :style="{ width: getwidth(index, value, 6) }" class="un_xg_1_3_7"></div>
                </div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, onBeforeMount } from "vue";
import store from '@/store'
import { queryGroupProject } from '@/script/api/common/commomApi'
import { addCollect } from '@/script/api/common/commomApi'
const emit = defineEmits(['showListInfo'])
const state = reactive({
  projectId: '',
  list: [],
  allNumber: 0,
  //全部项目
  allProject: [],
  buildingNumber: 0,
  //已开工项目
  buildingProject: [],
  delayNumber: 0,
  //延期项目
  delayProject: [],
  mainNumber: 0,
  //全国重点项日
  mainProject: [],
  noStartNumber: 0,
  //未开工项目
  noStartProject: [],
  accessNumber: 0,
  //已接入项目
  accessProject: [],
  class: 'button1',
  dataList: [
    { ename: "zhiding", name: "置顶" },
    { ename: "nanhaizhudao", name: "南海诸岛" },
    { ename: "beijing", name: "北京" },
    { ename: "tianjin", name: "天津" },
    { ename: "shanghai", name: "上海" },
    { ename: "chongqing", name: "重庆" },
    { ename: "hebei", name: "河北" },
    { ename: "henan", name: "河南" },
    { ename: "yunnan", name: "云南" },
    { ename: "liaoning", name: "辽宁" },
    { ename: "heilongjiang", name: "黑龙江" },
    { ename: "hunan", name: "湖南" },
    { ename: "anhui", name: "安徽" },
    { ename: "shandong", name: "山东" },
    { ename: "xinjiang", name: "新疆" },
    { ename: "jiangsu", name: "江苏" },
    { ename: "zhejiang", name: "浙江" },
    { ename: "jiangxi", name: "江西" },
    { ename: "hubei", name: "湖北" },
    { ename: "guangxi", name: "广西" },
    { ename: "gansu", name: "甘肃" },
    { ename: "jin", name: "山西" },
    { ename: "neimenggu", name: "内蒙古" },
    { ename: "shanxi", name: "陕西" },
    { ename: "jilin", name: "吉林" },
    { ename: "fujian", name: "福建" },
    { ename: "guizhou", name: "贵州" },
    { ename: "guangdong", name: "广东" },
    { ename: "qinghai", name: "青海" },
    { ename: "xizang", name: "西藏" },
    { ename: "sichuan", name: "四川" },
    { ename: "ningxia", name: "宁夏" },
    { ename: "hainan", name: "海南" },
    { ename: "taiwan", name: "台湾" },
    { ename: "xianggang", name: "香港" },
    { ename: "aomen", name: "澳门" },

  ],
})
onBeforeMount(async () => {
  const provinceKey = store.useProjectInfoStore.provinceKey
  if (provinceKey != '') {
    if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
      state.projectId = ""
      store.useProjectInfoStore.displayData[provinceKey].forEach(element => {
        state.projectId = state.projectId + element.projectId + ","
      });
      init();
    }
  }
})
const init = async () => {
  const params = {
    "platform": "1",
    "projectId": state.projectId
  }
  const { data } = await queryGroupProject(params)
  // state.allNumber = data.allProject.全部项目.length


  keyCirculate(data);
  state.allProject = getprojectList(data.allProject);
  state.buildingProject = getprojectList(data.buildingProject)
  state.delayProject = getprojectList(data.delayProject)
  state.mainProject = getprojectList(data.mainProject)
  state.noStartProject = getprojectList(data.noStartProject)
  state.accessProject = getprojectList(data.accessProject)

  if (state.class == "button1") {
    state.list = state.allProject;
  } else if (state.class == "button2") {
    state.list = state.delayProject;
  } else if (state.class == "button3") {
    state.list = state.mainProject;
  } else if (state.class == "button4") {
    state.list = state.noStartProject;
  } else if (state.class == "button5") {
    state.list = state.buildingProject;
  } else if (state.class == "button6") {
    state.list = state.accessProject;
  }
}
const keyCirculate = (data: any) => {
  state.allNumber = 0
  state.buildingNumber = 0
  state.delayNumber = 0
  state.mainNumber = 0
  state.noStartNumber = 0
  state.accessNumber = 0
  for (const key in data.allProject) {
    if (Object.prototype.hasOwnProperty.call(data.allProject, key)) {
      if (key != '置顶项目') {
        state.allNumber = data.allProject[key].length + state.allNumber;
      }
    }
  }

  for (const key in data.buildingProject) {
    if (Object.prototype.hasOwnProperty.call(data.buildingProject, key)) {
      if (key != '置顶项目') {
        state.buildingNumber = data.buildingProject[key].length + state.buildingNumber;
      }
    }
  }
  for (const key in data.delayProject) {
    if (Object.prototype.hasOwnProperty.call(data.delayProject, key)) {
      if (key != '置顶项目') {
        state.delayNumber = data.delayProject[key].length + state.delayNumber;
      }
    }
  }
  for (const key in data.mainProject) {
    if (Object.prototype.hasOwnProperty.call(data.mainProject, key)) {
      if (key != '置顶项目') {
        state.mainNumber = data.mainProject[key].length + state.mainNumber;
      }
    }
  }
  for (const key in data.noStartProject) {
    if (Object.prototype.hasOwnProperty.call(data.noStartProject, key)) {
      if (key != '置顶项目') {
        state.noStartNumber = data.noStartProject[key].length + state.noStartNumber;
      }
    }
  }
  for (const key in data.accessProject) {
    if (Object.prototype.hasOwnProperty.call(data.accessProject, key)) {
      if (key != '置顶项目') {
        state.accessNumber = data.accessProject[key].length + state.accessNumber;
      }
    }
  }
}
const getprojectList = (data: any) => {

  state.dataList.forEach(element => {
    for (let key in data) {
      if (data.hasOwnProperty.call(data, key)) {
        if (key.includes(element.name)) {
          data[element.name + "项目"] = data[key]
          if (key != "置顶项目") {
            delete data[key];
          }
        }
      }
    }
  });
  return data
}
const clickItem = (event) => {
  let items = document.querySelector('.top')?.children
  items = Array.from(items)
  items.forEach(item => {
    item.classList.remove('active')
  });
  state.class = event.target.classList[0]
  event.target.classList.add('active')

  if (event.target.id == "1") {
    state.list = state.allProject;
  } else if (event.target.id == "2") {
    state.list = state.delayProject;
  } else if (event.target.id == "3") {
    state.list = state.mainProject;
  } else if (event.target.id == "4") {
    state.list = state.noStartProject;
  } else if (event.target.id == "5") {
    state.list = state.buildingProject;
  } else if (event.target.id == "6") {
    state.list = state.accessProject;
  }
}
const iscollect = async (val: any) => {
  const params = {
    "projectId": val.projectId
  }
  const result = await addCollect(params)
  init()
}
const isNocollect = async (val: any) => {
  const params = {
    "projectId": val.projectId
  }
  const result = await addCollect(params)
  init()

}
const getwidth = (index: any, value: any, val: any) => {
  console.log(value)
  let width = 0 + '%'
  if (value[index].stageList != null) {
    if (value[index].stageList.length != 0) {
      width = value[index].stageList[val]?.stagePercent + '%'
    }
  }
  return width
}
// TODO:列表点击，回调父组件方法，显示项目详情
const showListInfo = (item: any) => {
  emit('showListInfo', item)
}
</script>
<style>
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: #165786;
  color: #BDD8EE;
}

.el-popper.is-customized .el-popper__arrow::before {
  background: #165786;
  right: 0;
}
</style>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top {
    width: 100%;
    display: flex;
    height: 150px;
    align-items: center;
    padding: 0 12px;


    .button1 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      .span1 {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #008CFF;
        opacity: 0.6;
        line-height: 20px;
      }

    }


    .button2 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      span {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #F7B500;
        opacity: 0.6;
        line-height: 20px;
      }
    }

    .button3 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      span {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #FF5F15;
        opacity: 0.6;
        line-height: 20px;
      }
    }

    .button4 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      span {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #FF5252;
        opacity: 0.6;
        line-height: 20px;
      }
    }

    .button5 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      span {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #02B65F;
        opacity: 0.6;
        line-height: 20px;
      }
    }

    .button6 {
      flex: 1;
      height: 54px;
      margin: 0px 12px 0px 12px;
      font-size: 14px;
      font-weight: 400;
      color: #9BB4C8;
      line-height: 16px;
      align-items: center;
      display: flex;
      justify-content: center;
      background: url(../../../assets/img/button-noselect.png);
      background-size: contain;

      span {
        padding-left: 16px;
        font-size: 20px;
        font-family: Regular;
        font-weight: 400;
        color: #02B65F;
        opacity: 0.6;
        line-height: 20px;
      }
    }

    .active {
      // background: #1957d2 !important;
      // border-color: #1957d2 !important;
      color: #00D4FF;
      background: url(../../../assets/img/button-select.png);
      background-size: contain;

      .span1 {
        color: #008CFF;
        opacity: 1;
      }

      .span2 {
        color: #F7B500;
        opacity: 1;
      }

      .span3 {
        color: #FF5F15;
        opacity: 1;
      }

      .span4 {
        color: #FF5252;
        opacity: 1;
      }

      .span5 {
        color: #02B65F;
        opacity: 1;
      }

      .span6 {
        color: #02B65F;
        opacity: 1;
      }
    }
  }

  .content-all {
    width: 100%;
    height: 100%;
    margin-bottom: 10px;

    .content-box {
      width: 100%;
      // height: 100%;
      margin-top: 10px;
      display: flex;

      .title-box {
        width: 144px;

        .title {
          position: relative;
          height: 100%;
          margin: 20px 0px 0 20px;
          padding-left: 10px;
          font-size: 18px;
          display: inline-block;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #D2EEFF;
          line-height: 16px;

          .color {
            height: 8px;
            background: #0033A8;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-top: -7px;
            margin-bottom: 24px;
          }
        }

      }



      .list-box {
        flex: 1;
        display: flex;
        justify-content: start;
        // margin-left: 20px;
        flex-wrap: wrap;

        .content-item {
          width: 30%;
          height: 34px;
          margin: 15px 0 15px 25px;
          position: relative;
        }

        .list-index {
          width: 100%;
          display: flex;

          .list-name {

            overflow: hidden; //溢出隐藏
            text-overflow: ellipsis; //属性值表示当对象内文本溢出时显示省略标记，省略标记插入的位置是最后一个字符。
            white-space: nowrap; //只保留一个空白，文本不会换行，会在在同一行上继续，直到遇到br标签为止。
            align-self: flex-start;
            margin-top: 5px;
            font-size: 16px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #6989A4;
            line-height: 16px;
          }

          .state-delay {
            margin-top: 6px;
            width: 14px;
            height: 14px;
            margin-left: 10px;
            right: 0px;
            background: url(../../../assets/img/general-extension-bg.png) no-repeat;
            background-size: contain;

          }

          .state-s-dealy {
            margin-top: 6px;
            width: 14px;
            height: 14px;
            margin-left: 10px;
            right: 0px;
            background: url(../../../assets/img/serious-delay-bg.png) no-repeat;
            background-size: contain;

          }

          .collect {
            position: absolute;
            margin-top: 5px;
            width: 14px;
            height: 14px;
            right: 0px;
            background: url(../../../assets/img/collect-bg.png) no-repeat;
            background-size: contain;
          }

          .collect-no {
            position: absolute;
            margin-top: 5px;
            width: 14px;
            height: 14px;
            right: 0px;
            background: url(../../../assets/img/no-collect-bg.png) no-repeat;
            background-size: contain;
          }

        }
      }

      .list-progress {
        position: relative;
        display: flex;
        margin-top: 11px;
        height: 10px;
        align-items: center;
        background: #252E44;
        width: 100%;

        .scheduleCont_1_left_1 {
          position: absolute;
          width: 100%;
          height: 6px;
          border-radius: 25px;
          display: flex;
          align-items: center;


          .un_xg_1_3_1 {
            height: 6px;
            background: #1559FF;
          }

          .un_xg_1_3_2 {
            height: 6px;
            background: #46970E;
          }

          .un_xg_1_3_3 {
            height: 6px;
            background: #FF7926;
          }

          .un_xg_1_3_4 {
            height: 6px;
            background: #00D3FF;
          }

          .un_xg_1_3_5 {
            height: 6px;
            background: #59B4FF;
          }

          .un_xg_1_3_6 {
            height: 6px;
            background: #74E870;
          }

          .un_xg_1_3_7 {
            height: 6px;
            background: #FFCD43;
          }
        }

      }

    }
  }
}
</style>
