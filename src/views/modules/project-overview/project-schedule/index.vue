<template>
  <div v-if="showMapFlag">
    <ProjectOverviewDetails :project-id="itemProjectId" @show-details-info="showDetailsInfo"></ProjectOverviewDetails>
  </div>
  <div class="container-ps">
    <div class="return-btn" @click="bigReturn"></div>
    <div class="ps-left">
      <div class="ps-l-top">
        <EchartsStatistics>
          <div class="echarts-statistics-header"></div>
        </EchartsStatistics>
      </div>
      <div class="ps-l-bottom">
        <ImportantEvents>
          <div class="echarts-events-header"></div>
        </ImportantEvents>
      </div>
    </div>
    <div class="ps-right">
      <projectList @show-list-info="showListInfo"></projectList>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted, watch, ref } from "vue";
import ProjectOverviewDetails from "../project-overview-details/index.vue";
import EchartsStatistics from '../../components/EchartsStatistics.vue';
import ImportantEvents from './../important-events/index.vue';
import projectList from './ProjectList.vue';
let showMapFlag = ref(false)
let itemProjectId = ref('')
const emit = defineEmits(['showPoInfo'])

const bigReturn = () => {
  emit('showPoInfo', false)
}

/**
 * 项目列表子组件回调方法
 * @param val
 */
const showListInfo = (val: any) => {
  console.log('val: ', val);
  itemProjectId.value = val.projectId
  showMapFlag.value = true
}
/**
 * 项目详情子组件回调方法
 * @param val
 */
const showDetailsInfo = (val: boolean) => {

  showMapFlag.value = val
}
</script>

<style lang="scss" scoped>
@mixin border-same {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1), rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)) 1 1;
}

@mixin echarts-header {
  width: 100%;
  height: 60px;
}

.container-ps {
  display: flex;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1810px;
  height: 958px;
  background: #182137;
  border-radius: 8px;

  .return-btn {
    position: absolute;
    // z-index: 100;
    top: -35px;
    right: 0px;
    width: 20px;
    height: 20px;
    background: url(../../../assets/img/back.png);
  }

  .ps-left {
    width: 520px;
    height: 100%;
    margin: 15px 5px 5px 16px;

    .ps-l-top {
      width: 100%;
      height: 310px;
      @include border-same;

      .echarts-statistics-header {
        @include echarts-header;
        background: url(../../../assets/img/statistics_title_bg.png) no-repeat;

      }
    }

    .ps-l-bottom {
      margin-top: 10px;
      width: 100%;
      height: 616px;
      @include border-same;

      .echarts-events-header {
        @include echarts-header;
        background: url(../../../assets/img/event_bg2.png) no-repeat;
      }
    }
  }

  .ps-right {
    flex: 1;
    height: 936px;
    margin: 15px 16px 5px 5px;
    @include border-same;
  }
}</style>
