<template>
  <div class="china-container">
    <!-- 中国地图 一级页面 -->
    <div class="china-map">
      <!-- <img class="showChinaImg" :src="mapUrl" alt="" id="hoverId"> -->
      <img class="hover" :src="mapHoverUrl" alt="" id="hoverId">
      <canvas
            class="draw-canvas"
            v-if="mapHoverUrl.length"
            id="tutorialDrawing"
            :width="imageWidth"
            :height="imageHeight"
          ></canvas>
    </div>
  </div>
  <div class="nanhai">
    <button @click="drawBtn">开启画图</button>
    <button @click="getData">获取coord数据</button>
    <button @click="getOpposeCoordData">获取opposeCoord数据</button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch, ref, nextTick } from "vue";
import { useRouter } from "vue-router";
import store from '@/store'
import ChinaMapDefault from '../../../assets/img/<EMAIL>'
import HeiLongJiang from '../../../assets/img/map/heilongjiang.png'
import BeiJing from '../../../assets/img/map/beijing.png'
import TianJin from '../../../assets/img/map/tianjin.png'
import shandong from '../../../assets/img/map/taiwan.png'
import * as _ from 'lodash'

const OPEANTIONTYPE = {
  normal: 1, //正常操作
  drag: 2, //拖拽定点编辑
  move: 3 //移动整个图形
};

const router = useRouter();

let mapUrl = ref(ChinaMapDefault) // 默认地图

let mapHoverUrl = ref(shandong) // 省份地图

let imageHeight = ref(0) //图片高度
let imageWidth = ref(0) //图片宽度
let imageOriginalWidth = 0 //图片原始宽度
let imageOriginalHeight = 0 //图片原始高度
let pointArrayLsit: any[] = [] //记录一个区域的路径点 (临时保存)
let allPointList: any[] = []  //记录所有区域的路径点
let ctxObject: CanvasRenderingContext2D | null = null //绘图对象
let isCanDraw = ref(false) //是否可以绘制
let currentClickedPoint: { x: number; y: number; } | null = null //当前鼠标左键点击的点
let opeantionType = 1 //操作类型
let dragLocation: { parentIndex: string | number; sonIndex: string | number; } | null = null //记录要拖拽的边角点
let willMovingIndex: string | number | null = null //记录即将要移动的图像索引
let lastMovePoint: { x: number; y: number; } | null = null //记录上次移动的点 (平移时使用)
let currentQueryDetailInfo = null //记录当前的详情
let lastPictureWidth = 0 //记录上次的图片宽度
let lastPictureHeight = 0 //记录上次的图片高度

let showFlag = ref(false)

const drawBtn = () => {
  isCanDraw.value = true
}
const getData = () => {
  const result = transedCurrent_Otiginal()
  console.log('transedCurrent_Otiginal: ', result);
}

const getOpposeCoordData = () => {
  const result = transedCurrent_Otiginal_percent()
  console.log('transedCurrent_Otiginal: ', result);
  localStorage.setItem('OpposeCoord', JSON.stringify(result));
}

// const showProvinceDetail = () => {
//   router.push({
//       path: "/province-map",
//       query: { },
//     });
// }
const handleMouseEvent = (e: any) => {
  console.log('handleMouseEvent: ', e)
  console.log('imageWidth.value: ', imageWidth.value);
  console.log('mageHeight.value: ', imageHeight.value);
  let tmpDict = {
    x: e.offsetX / imageWidth.value,
    y: e.offsetY / imageHeight.value
  };
  // allPointList
  let myData = localStorage.getItem('OpposeCoord')
  console.log('myData: ', myData);
  let myParseData = JSON.parse(myData)
  console.log('myParseData: ', myParseData);
  allPointList = myParseData
  console.log('tmpDict: ', tmpDict);
  const flag = checkPointIsInPolygon(tmpDict)
  console.log('flag: ', flag);
  showFlag.value = flag
}

onMounted(() => {
  getPictureSize()
  //监听浏览器的缩放
  window.onresize = function () {
    console.log('------浏览器在缩放------');
    if (allPointList.length > 0) {
      let img = document.getElementById('hoverId') as HTMLImageElement
      imageWidth.value = img.offsetWidth
      imageHeight.value = img.offsetHeight
      imageOriginalWidth = img.naturalWidth
      imageOriginalHeight = img.naturalHeight
      customDrawingInitMenthod()
      setTimeout(() => {
        zoom_TransedOriginal_current()
        //重新绘制，（延迟一会儿绘制,不然会出问题）
        let ctx = ctxObject
        ctx?.clearRect(0, 0, imageWidth.value, imageHeight.value)
        drawSavedPathMenthod()
        lastPictureWidth = imageWidth.value
        lastPictureHeight = imageHeight.value
      }, 1)
    }
  };
});


/**================================================== ⬇ 画图处理相关 ⬇ ================================================== */
/*
 * 获取图片大小
 **/
const getPictureSize = () => {
  nextTick(() => {
    setTimeout(() => {
      let img = document.getElementById('hoverId') as HTMLImageElement
      imageWidth.value = img.offsetWidth
      imageHeight.value = img.offsetHeight
      lastPictureWidth = imageWidth.value
      lastPictureHeight = imageHeight.value
      imageOriginalWidth = img.naturalWidth
      imageOriginalHeight = img.naturalHeight
      console.log('--宽度---' + imageWidth.value);
      console.log('--高度---' + imageHeight.value);
      console.log('-原始-宽度---' + img.naturalWidth);
      console.log('-原始-高度---' + img.naturalHeight);
      customDrawingInitMenthod()
      setTimeout(() => {
        transedOriginal_current()
        //重新绘制，（延迟一会儿绘制,不然会出问题）
        let ctx = ctxObject
        ctx?.clearRect(0, 0, imageWidth.value, imageHeight.value)
        drawSavedPathMenthod()
      }, 2)
    }, 150)
  });
}

/**
 * 根据图片的原始宽度和高度计算
 * 转换成 --> 原图片上的坐标点
 *  */
const transedCurrent_Otiginal = () => {
  let tmpArray = _.cloneDeep(allPointList)
  let widthCompare = imageOriginalWidth / imageWidth.value
  let heightCompare = imageOriginalHeight / imageHeight.value
  for (let index = 0; index < tmpArray.length; index++) {
    const element = tmpArray[index]
    for (let index = 0; index < element.length; index++) {
      let anotherElement = element[index]
      /**有个坑 （必须转换成Int类型） */
      anotherElement.x = parseInt((anotherElement.x * widthCompare).toFixed(0))
      anotherElement.y = parseInt((anotherElement.y * heightCompare).toFixed(0))
    }
  }
  return tmpArray
}

const transedCurrent_Otiginal_percent = () => {
  let tmpArray = _.cloneDeep(allPointList);
  let widthCompare = imageOriginalWidth / imageWidth.value;
  let heightCompare = imageOriginalHeight / imageHeight.value;
  for (let index = 0; index < tmpArray.length; index++) {
    const element = tmpArray[index];
    for (let index = 0; index < element.length; index++) {
      let anotherElement = element[index];
      /**有个坑 （必须转换成Int类型） */
      anotherElement.x = parseInt((anotherElement.x * widthCompare).toFixed(0));
      anotherElement.y = parseInt((anotherElement.y * heightCompare).toFixed(0));

      //-----------------❎测试代码❎------------------
      anotherElement.x = Number(
        (anotherElement.x / imageOriginalWidth).toFixed(10)
      );
      anotherElement.y = Number(
        (anotherElement.y / imageOriginalHeight).toFixed(10)
      );
    }
  }
  return tmpArray
}

/**
 * 根据图片的原始宽度和高度计算
 * 转换成 --> 当前显示的图片上的坐标点
 * */
const transedOriginal_current = () => {
  let widthCompare = imageWidth.value / imageOriginalWidth
  let heightCompare = imageHeight.value / imageOriginalHeight
  for (let index = 0; index < allPointList.length; index++) {
    const element = allPointList[index]
    for (let index = 0; index < element.length; index++) {
      let anotherElement = element[index]
      /**有个坑 （必须转换成Int类型） */
      anotherElement.x = parseInt((anotherElement.x * widthCompare).toFixed(0))
      anotherElement.y = parseInt((anotherElement.y * heightCompare).toFixed(0))

      //-----------------❎测试代码❎------------------
      // anotherElement.x = parseInt((anotherElement.x * this.imageWidth).toFixed(0));
      // anotherElement.y = parseInt((anotherElement.y * this.imageHeight).toFixed(0));
    }
  }
  return allPointList;
}

/**
 * 根据图片的原始宽度和高度计算
 * (屏幕缩放时使用)
 * 转换成 --> 当前缩放的坐标点
 * */
const zoom_TransedOriginal_current = () => {
  let widthCompare = imageWidth.value / lastPictureWidth
  let heightCompare = imageHeight.value / lastPictureHeight
  for (let index = 0; index < allPointList.length; index++) {
    const element = allPointList[index];
    for (let index = 0; index < element.length; index++) {
      let anotherElement = element[index];
      /**有个坑 （必须转换成Int类型） */
      anotherElement.x = parseInt((anotherElement.x * widthCompare).toFixed(0));
      anotherElement.y = parseInt((anotherElement.y * heightCompare).toFixed(0));
    }
  }
  return allPointList;
}

/**
 * 初始化绘图实例类
 * */
const customDrawingInitMenthod = () => {
  console.log('customDrawingInitMenthod');
  var canvas = document.getElementById('tutorialDrawing') as HTMLCanvasElement
  var ctx = canvas.getContext('2d') as CanvasRenderingContext2D
  ctx.lineWidth = 2
  ctx.strokeStyle = '#F5222D'
  ctx.fillStyle = 'transparent'
  ctx.lineJoin = 'round'
  ctx.lineCap = 'round'
  ctxObject = ctx

  /** 禁止右键菜单 */
  canvas.oncontextmenu = function (event) {
    event.preventDefault()
  }

  /** 鼠标按钮被按下 */
  console.log('canvas: ', canvas);
  canvas.onmousedown = function (e) {
    console.log(e.offsetX + '--' + e.offsetY)
    let tmpDict = {
      x: e.offsetX,
      y: e.offsetY
    }
    currentClickedPoint = tmpDict
    if (e.button === 0) {
      mouseLeftClickedMenthod(tmpDict)
    } else if (e.button === 2) {
      mouseRightClickedMenthod(tmpDict)
    }
  };

  /** 鼠标按键被松开 */
  canvas.onmouseup = function (e) {
    if (opeantionType == OPEANTIONTYPE.drag) {
      isCanDraw.value = false
      dragLocation = null
      pointArrayLsit = []
    } else if (opeantionType == OPEANTIONTYPE.move) {
      isCanDraw.value = false
    }
  };

  /** 鼠标被移动 */
  canvas.onmousemove = function (e) {
    let tmpDict = {
      x: e.offsetX,
      y: e.offsetY
    };
    if (isCanDraw.value) {
      mouseMovedMenthod(tmpDict)
    }
  };
}

/**
 * 绘制图片方法
 * （正常绘制）
 */
const drawMenthod = (moviedPoint: { x: any; y: any; }) => {
  console.log('正常绘制')
  let ctx = ctxObject as CanvasRenderingContext2D
  ctx.lineWidth = 2
  ctx.strokeStyle = '#F5222D'
  ctx.fillStyle = 'transparent'
  ctx.lineJoin = 'round'
  ctx.lineCap = 'round'
  ctx.clearRect(0, 0, imageWidth.value, imageHeight.value)
  ctx.beginPath()
  for (let index = 0; index < pointArrayLsit.length; index++) {
    const element = pointArrayLsit[index]
    if (index == 0) {
      ctx.moveTo(element.x, element.y)
    } else {
      ctx.lineTo(element.x, element.y)
    }
  }
  if (moviedPoint) {
    ctx.lineTo(moviedPoint.x, moviedPoint.y)
  }
  ctx.closePath()
  ctx.stroke()

  //绘制圆点
  ctx.beginPath()
  for (let index = 0; index < pointArrayLsit.length; index++) {
    const element = pointArrayLsit[index]
    ctx.beginPath()
    ctx.arc(element.x, element.y, 6, 0, Math.PI * 2, true)
    ctx.closePath()
    ctx.stroke()
  }
  if (moviedPoint) {
    ctx.beginPath()
    ctx.arc(moviedPoint.x, moviedPoint.y, 6, 0, Math.PI * 2, true)
    ctx.closePath()
    ctx.stroke()
  }

  //绘制之前的所有路径
  drawSavedPathMenthod()
}

/**
 * 绘制之前保存的图片，
 * (一张图中可绘制多个区域)
 * */
const drawSavedPathMenthod = () => {
  let ctx = ctxObject as CanvasRenderingContext2D
  ctx.lineWidth = 2
  ctx.strokeStyle = '#F5222D'
  ctx.fillStyle = 'transparent'
  ctx.lineJoin = 'round'
  ctx.lineCap = 'round'
  for (let index = 0; index < allPointList.length; index++) {
    ctx.beginPath()
    const anotherelement = allPointList[index]
    for (let index = 0; index < anotherelement.length; index++) {
      const element = anotherelement[index]
      if (index == 0) {
        ctx.moveTo(element.x, element.y)
      } else {
        ctx.lineTo(element.x, element.y)
      }
    }
    ctx.closePath()
    ctx.stroke()
  }

  for (let index = 0; index < allPointList.length; index++) {
    const anotherelement = allPointList[index]
    for (let index = 0; index < anotherelement.length; index++) {
      const element = anotherelement[index]
      ctx.beginPath()
      ctx.arc(element.x, element.y, 6, 0, Math.PI * 2, true)
      ctx.closePath()
      ctx.stroke()
    }
  }
}

/**
 * 拖拽绘制
 * (拖动某一个顶点)
 * */
const dragDrawingMenthod = (moviedPoint: any) => {
  //替换拖拽的点
  let selectedShape = allPointList[dragLocation.parentIndex]
  selectedShape[dragLocation.sonIndex] = moviedPoint
  //开始绘制
  let ctx = ctxObject as CanvasRenderingContext2D
  ctx.clearRect(0, 0, imageWidth.value, imageHeight.value)
  drawSavedPathMenthod()
}

/**
 *  移动绘制
 * （移动整个图形）
 **/
const moveDrawingenthod = (moviedPoint: { x: number; y: number; }) => {
  let originalMovePoint = lastMovePoint ? lastMovePoint : currentClickedPoint
  lastMovePoint = moviedPoint

  //获取坐标差
  let differX = moviedPoint.x - originalMovePoint.x
  let differY = moviedPoint.y - originalMovePoint.y
  let selectedShape = allPointList[willMovingIndex]

  //判断图形是否超出边界,（超出边界禁止绘制）
  for (let index = 0; index < selectedShape.length; index++) {
    const element = selectedShape[index]
    if (
      element.x + differX < 0 ||
      element.x + differX > imageWidth.value ||
      element.y + differY < 0 ||
      element.y + differY > imageHeight.value
    ) {
      return
    }
  }
  //横纵坐标相加
  for (let index = 0; index < selectedShape.length; index++) {
    const element = selectedShape[index]
    element.x = element.x + differX
    element.y = element.y + differY
  }

  let ctx = ctxObject as CanvasRenderingContext2D
  ctx.clearRect(0, 0, imageWidth.value, imageHeight.value)
  drawSavedPathMenthod()
}

/**
 * 判断操作类型
 * (1.正常绘制，2.拖拽边角，3.移动整个图像)
 * */
const judgeOpeantionType = () => {
  if (isBoundaryPoint(currentClickedPoint)) {
    opeantionType = OPEANTIONTYPE.drag
  } else if (checkPointIsInPolygon(currentClickedPoint)) {
    opeantionType = OPEANTIONTYPE.move
  } else {
    opeantionType = OPEANTIONTYPE.normal
  }
  return opeantionType
}

/** 鼠标左键单击时的操作 */
const mouseLeftClickedMenthod = (clickedPoint: { x: number; y: number; }) => {
  judgeOpeantionType()
  if (opeantionType == OPEANTIONTYPE.normal) {
    //--------普通绘制--------
    isCanDraw.value = true
    pointArrayLsit.push(clickedPoint)
    drawMenthod()
  } else if (opeantionType == OPEANTIONTYPE.drag) {
    //--------开始拖拽--------
    isCanDraw.value = true
  } else if (opeantionType == OPEANTIONTYPE.move) {
    //--------开始移动--------
    lastMovePoint = null
    isCanDraw.value = true
  }
}

/** 鼠标右键单击时的操作 */
const mouseRightClickedMenthod = (clickedPoint: { x: number; y: number; }) => {
  if (opeantionType == OPEANTIONTYPE.normal) {
    isCanDraw.value = false
    allPointList.push(pointArrayLsit)
    pointArrayLsit = []
  } else if (opeantionType == OPEANTIONTYPE.drag) {
  } else if (opeantionType == OPEANTIONTYPE.move) {
  }
}

/** 鼠标移动时的操作 */
const mouseMovedMenthod = (moviedPoint: { x: number; y: number; }) => {
  if (opeantionType == OPEANTIONTYPE.normal) {
    //------移动--正常绘制--------
    drawMenthod(moviedPoint)
  } else if (opeantionType == OPEANTIONTYPE.drag) {
    //------移动--拖拽绘制--------
    dragDrawingMenthod(moviedPoint)
  } else if (opeantionType == OPEANTIONTYPE.move) {
    //------移动--移动绘制--------
    moveDrawingenthod(moviedPoint)
  }
}

/**================================================== ⬇ 判断边界 ⬇ ================================================== */
/**
 * 判断某一点是否在图形的边界点，并获取拖拽点的位置
 * (拖拽边界点使用)
 * */
const isBoundaryPoint = (clickedPoint: { x: number; y: number; }) => {
  for (let index = 0; index < allPointList.length; index++) {
    const anotherelement = allPointList[index]
    for (let sonindex = 0; sonindex < anotherelement.length; sonindex++) {
      const element = anotherelement[sonindex]
      let left = element.x - 5
      let right = element.x + 5
      let top = element.y - 5
      let bottom = element.y + 5
      if (
        clickedPoint.x > left &&
        clickedPoint.x < right &&
        clickedPoint.y > top &&
        clickedPoint.y < bottom
      ) {
        let tmpDict = {
          parentIndex: index,
          sonIndex: sonindex
        };
        dragLocation = tmpDict
        return true
      }
    }
  }
  return false;
}

/**
 * 测试某点是否在多边形内
 * (平移时使用)
 * */
const checkPointIsInPolygon = (moviedPoint: { x: number; y: number; } | null) => {
  let flag = false;
  let checkPoint = [moviedPoint.x, moviedPoint.y];
  for (let index = 0; index < allPointList.length; index++) {
    const element = allPointList[index];
    let polygonPoints = [];
    for (let index = 0; index < element.length; index++) {
      const sonElement = element[index];
      let tmpArray = [sonElement.x, sonElement.y];
      polygonPoints.push(tmpArray);
    }
    if (isInPolygon(checkPoint, polygonPoints)) {
      //记录要移动的图形索引
      flag = true;
      willMovingIndex = index;
    }
  }
  return flag;
}

/**
 * 判断某个点是否在多边形区域内
 **/
const isInPolygon = (checkPoint: number[], polygonPoints: string | any[]) => {
  var counter = 0;
  var i;
  var xinters;
  var p1, p2;
  var pointCount = polygonPoints.length;
  p1 = polygonPoints[0];

  for (i = 1; i <= pointCount; i++) {
    p2 = polygonPoints[i % pointCount];
    if (checkPoint[0] > Math.min(p1[0], p2[0]) && checkPoint[0] <= Math.max(p1[0], p2[0])) {
      if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
        if (p1[0] != p2[0]) {
          xinters = ((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) + p1[1];
          if (p1[1] == p2[1] || checkPoint[1] <= xinters) {
            counter++;
          }
        }
      }
    }
    p1 = p2;
  }
  if (counter % 2 == 0) {
    return false;
  } else {
    return true;
  }
}



</script>
<style scoped lang="scss">
.china-container {
  width: 100%;
  height: 100%;

  .china-map {
    position: relative;
    top: 10px;
    width: 100%;
    height: calc(100% - 10px);
    display: flex;
    align-items: center;

    .showChinaImg {
      width: 770px;
      height: 540px;
      object-fit: contain;
    }

    .hover {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
      // z-index: 100;
      width: 770px;
      height: 540px;
      opacity: 0.6;
      object-fit: contain;
    }

    .draw-canvas {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
    }
  }
}

.nanhai {
  position: absolute;
  right: 105px;
  bottom: 66px;
  width: 110px;
  height: 160px;
  background: url(../../../assets/img/<EMAIL>) no-repeat;
  background-size: cover;
}
</style>
