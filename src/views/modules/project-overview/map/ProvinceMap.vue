<template>
  <div class="province-container">
    <!-- 中国省级地图 二级页面 -->
    <div class="province-map">
      <div class="showImg" @mousedown="testPoint">
        <img :src="mapProvinceUrl" id="provinceId">
        <!-- <img class="logo-flag" :src="flagIcon" alt=""> -->
        <!-- 向上提示框 -->
        <div v-show="promptFlag && directionFlag" class="anchor-point-province"
          :style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
          <div class="anchor-bg-province">
            <div>{{ projectState.name }}</div>
            <div>状态：<span>{{ projectState.dalayLevel }}</span></div>
          </div>
          <div class="anchor-line-province"></div>
        </div>
        <!-- 向下s提示框 -->
        <div v-show="promptFlag && !directionFlag" class="anchor-point-province"
          :style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
          <div class="anchor-line-reserve-province"></div>
          <div class="anchor-bg-province">
            <div>{{ projectState.name }}</div>
            <div>状态：<span>{{ projectState.dalayLevel }}</span></div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-if="showFlag" class="returnBtn" @click="returnChinaBtn">
      返回
    </div> -->
  </div>

  <ProjectOverviewDetails :project-id="projectId" @show-details-info="returnDetails" v-if="showDetails">
  </ProjectOverviewDetails>
</template>

<script setup lang="ts">
import ProjectOverviewDetails from '../project-overview-details/index.vue'
import { onMounted, reactive, watch, ref } from "vue"
import { useRouter } from "vue-router"
import { useProjectInfoStore } from '@/store/modules/project'
import { saveProjectMarkApi } from '@/script/api/common/commomApi'

const router = useRouter()
const projectInfoStore: any = useProjectInfoStore()

interface Props {
  provinceCode: string
  provinceName: string
  flag: string
}

const props = withDefaults(defineProps<Props>(), {
  provinceCode: '',
  provinceName: '',
  flag: '0'
})

let mapProvinceUrl = ref() // 省级地图链接
let showFlag = ref(true) // 返回按钮显示标记
let imageWidth = ref(0) // 省级图片宽度
let imageHeight = ref(0) // 省级图片高度
let promptFlag = ref(false) // 提示框标记
let directionFlag = ref(true) // 提示框方向标记（true:上方显示;false:下方显示）
let offsetWidths = ref(0) // 偏移宽度
let offsetHeights = ref(0) //偏移高度
let projectState = reactive({
  name: '',
  dalayLevel: ''
})
let showDetails = ref(false) // 是否展示项目详情框
let projectId = ref('') // 项目ID

onMounted(() => {
  createFlagPoint()
  if (props.flag == "1") { // 直接路由进入时，不显示
    showFlag.value = false
  } else { // 从中国地图页进入时显示
    showFlag.value = true
  }
  mapProvinceUrl.value = getAssetsURL(props.provinceCode)
})

const proInfo = reactive({
  disData: projectInfoStore.displayData,
})
watch(() => projectInfoStore.displayData, (newValue, oldValue) => {
  proInfo.disData = newValue
}, { deep: true });

/**
 * 返回按钮
 */
const returnChinaBtn = async () => {
  projectInfoStore.cityLogoFlag = '0'
  const param = {}
  await projectInfoStore.getProjectInfoAction(param)
  projectInfoStore.provinceKey = '全国'
  projectInfoStore.titleKey = '全国'
  router.go(-1)
}

/**
 * 根据名称获取图片路径
 * @param imageName 图片名称
 */
const getAssetsURL = (imageName: string) => {
  return new URL(`../../../assets/img/province/${imageName}.png`, import.meta.url).href
}

/**
 * 创建标记图标点位(🚩)
 */
const createFlagPoint = () => {
  const provinceImg = document.getElementById('provinceId') as HTMLImageElement
  imageWidth.value = provinceImg.offsetWidth
  console.log('imageWidth.value: ', imageWidth.value)
  imageHeight.value = provinceImg.offsetHeight
  console.log('imageHeight.value: ', imageHeight.value)
  const currentDiv = document.querySelector('.showImg') as HTMLDivElement // 获取父视图，把点位及点位名称显示到父视图上
  for (const key in proInfo.disData) {
    if (Object.prototype.hasOwnProperty.call(proInfo.disData, key)) {
      if (!key.includes('count')) {
        const element = proInfo.disData[key];
        element.forEach(flagItem => {
          // 创建点位
          const imgEle = document.createElement('img')
          imgEle.setAttribute(
            'style',
            'width:36px;height:42px;position:absolute;cursor:pointer;'
          )
          imgEle.className = 'flag'
          let imageStr = getImageFromType(flagItem.delayLevel) as string;
          imgEle.setAttribute('src', imageStr);
          imgEle.ondragstart = function () {
            return false;
          }; // 取消图片的默认拖拽行为
          imgEle.style.left = `${flagItem.coordinateX * imageWidth.value}px`
          imgEle.style.top = `${flagItem.coordinateY * imageHeight.value }px`
          imgEle.addEventListener(
            'click',
            e => {
              console.log('dainjile')
              projectId.value = flagItem.projectId
              showDetails.value = true
            },
            false
          )
          currentDiv.appendChild(imgEle);
          //悬停操作
          imgEle.onmouseover = function (e) {
            projectState.name = flagItem.shortName // 鼠标悬停时，赋值项目名称
            const level = getInsFromType(flagItem.delayLevel) as string
            console.log('flagItem: ', flagItem);
            console.log('level: ', level);
            projectState.dalayLevel = level
            promptFlag.value = true
            offsetWidths.value = flagItem.coordinateX * imageWidth.value - 100 // 偏移量需要减去自身宽度的一半
            if (flagItem.coordinateY * imageHeight.value > 130) { // 若偏移量大于自身高度，则显示方向为下的布局
              offsetHeights.value = flagItem.coordinateY * imageHeight.value - 140  // 偏移量需要减去一定的高度
              directionFlag.value = true
            } else {
              offsetHeights.value = flagItem.coordinateY * imageHeight.value  // 偏移量需要增加一定的高度
              directionFlag.value = false
            }
          }
          imgEle.onmouseleave = function () {
            promptFlag.value = false
          }
        })
      }
    }
  }
}

/**
 * 测试用（查看鼠标所点击的点位偏移量）
 * @param e 
 */
const testPoint = async (e: any) => {
  console.log('e.offsetX: ', e.offsetX);
  console.log('e.offsetY: ', e.offsetY);
  let tmpDict = {
    x: e.offsetX / imageWidth.value,
    y: e.offsetY / imageHeight.value
  }
  console.log('tmpDict: ', tmpDict);
  let params = {
    marks: [
      {
        "coordinateX": tmpDict.x + "",
        "coordinateY": tmpDict.y + "",
        "projectId": "35craft", // 18craft 22craft 30craft 31craft 33craft
        "platform": "1"
      }
    ]
  }
  // const result = await saveProjectMarkApi(params)
  // console.log('result: ', result);
}

/**
 * 通过类型获取对应的小旗子图标
 * @param type 类型
 */
const getImageFromType = (type: string) => {
  switch (type) {
    case '0': // 正常
      return new URL('../../../assets/img/flag_normal.png', import.meta.url).href
    case '1': // 延期
      return new URL('../../../assets/img/flag_delay.png', import.meta.url).href
    case '2': // 严重延期
      return new URL('../../../assets/img/flag_s_delay.png', import.meta.url).href
    case '3': // 未接入
      return new URL('../../../assets/img/flag_no_access.png', import.meta.url).href
  }
}


/**
 * 通过类型获取描述
 * @param type 类型
 */
const getInsFromType = (type: string) => {
  console.log('type: ', type);
  switch (type) {
    case '0': // 正常
      return '正常'
    case '1': // 延期
      return '延期'
    case '2': // 严重延期
      return '严重延期'
    case '3': // 严重延期
      return '未纳管'
  }
}

const returnDetails = (flag: boolean) => {
  console.log('flag: ', flag);
  showDetails.value = flag
}

</script>
<style scoped lang="scss">
.province-container {
  width: 100%;
  height: calc(100% - 10px);

  .province-map {
    margin-top: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    .showImg {
      position: relative;
      width: 770px;
      height: 535px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .logo-flag {
        position: absolute;
        left: 392px;
        top: 402px;
        width: 36px;
        height: 42px;
      }
    }

    .anchor-point-province {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 220px;
      height: 140px;

      .anchor-bg-province {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 55px;
        color: #9ED7FF;
        font-size: 14px;
        background: url(../../../assets/img/anchor_bg_big2.png) no-repeat;
        background-size: cover;

        span {
          color: #f7b500;
        }
      }

      .anchor-line-province {
        width: 1px;
        height: 85px;
        background: linear-gradient(#70B9FE, #025565)
      }

      .anchor-line-reserve-province {
        width: 1px;
        height: 85px;
        background: linear-gradient(#025565, #70B9FE)
      }
    }

  }

  .returnBtn {
    position: absolute;
    z-index: 1;
    top: 40px;
    right: 40px;
    width: 92px;
    height: 36px;
    background: #384D7E;
    font-family: PingFangSC-Regular;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '';
      margin: 0 10px 3px 0;
      display: inline-block;
      width: 20px;
      height: 16px;
      background: url(../../../assets/img/return.png) no-repeat;
    }
  }
}
</style>
