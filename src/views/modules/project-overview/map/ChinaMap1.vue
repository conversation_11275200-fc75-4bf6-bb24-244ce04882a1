<template>
	<div class="china-container">
		<!-- 中国地图 一级页面 -->

		<div
			class="china-map"
			@mousemove.prevent="debouncedFnAll">
			<!-- <div
				class="returnBtn"
				@click="returnChinaBtn"
				v-show="isShowBtn">
				返回
			</div> -->
			<img
				class="showChinaImg"
				:src="mapUrl"
				alt=""
				id="chinaDefaultMap" />
			<img
				v-if="showFlag"
				class="hover"
				:src="mapHoverUrl"
				alt=""
				@click="showProvinceDetail" />

			<!-- 向上提示框  -->
			<div
				v-show="promptFlag && directionFlag"
				class="anchor-point"
				:style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
				<div class="anchor-bg">
					{{ provinceInfo[0]?.name }}：<span>{{ statePro.proInfo.total >= 0 ? statePro.proInfo.total : 0 }}</span>
				</div>
				<div class="anchor-line"></div>
			</div>
			<!-- 向下提示框  -->
			<div
				v-show="promptFlag && !directionFlag"
				class="anchor-point-reserve"
				:style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
				<div class="anchor-line-reserve"></div>
				<div class="anchor-bg-reserve">
					{{ provinceInfo[0]?.name }}：<span>{{ statePro.proInfo.total >= 0 ? statePro.proInfo.total : 0 }}</span>
				</div>
			</div>
		</div>
	</div>
	<div class="nanhai"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, watch, ref, nextTick, onBeforeMount } from 'vue'
import { useRouter } from 'vue-router'
import store from '@/store'
import ChinaMapDefault from '../../../assets/img/<EMAIL>'
import ChinaData from '@/assets/json/china.json'
import provinceRotation from '@/assets/json/provinceRotation.json'
import { useDebounceFn } from '@vueuse/core'

const router = useRouter()

let mapUrl = ref(ChinaMapDefault) // 默认中国地图

let mapHoverUrl = ref() // 鼠标悬停时所展示的省份地图

let showFlag = ref(false) // 是否显示省份地图图片

let isShowBtn = ref(false) // 是否显示返回按钮

let imageHeight = ref(0) //图片高度

let imageWidth = ref(0) //图片宽度

let allPointList: any[] = [] //记录所有区域的路径点

let provinceKey = ref() //省份名称

let offsetWidths = ref(0) // 偏移宽度

let offsetHeights = ref(0) //偏移高度

let promptFlag = ref(false) // 提示框标记

let directionFlag = ref(true) // 提示框方向标记（true:上方显示;false:下方显示）

let state = reactive({
	// ename为了获取省份的名字
	dataList: [
		{ ename: 'nanhaizhudao', name: '南海诸岛' },
		{ ename: 'beijing', name: '北京' },
		{ ename: 'tianjin', name: '天津' },
		{ ename: 'shanghai', name: '上海' },
		{ ename: 'chongqing', name: '重庆' },
		{ ename: 'hebei', name: '河北' },
		{ ename: 'henan', name: '河南' },
		{ ename: 'yunnan', name: '云南' },
		{ ename: 'liaoning', name: '辽宁' },
		{ ename: 'heilongjiang', name: '黑龙江' },
		{ ename: 'hunan', name: '湖南' },
		{ ename: 'anhui', name: '安徽' },
		{ ename: 'shandong', name: '山东' },
		{ ename: 'xinjiang', name: '新疆' },
		{ ename: 'jiangsu', name: '江苏' },
		{ ename: 'zhejiang', name: '浙江' },
		{ ename: 'jiangxi', name: '江西' },
		{ ename: 'hubei', name: '湖北' },
		{ ename: 'guangxi', name: '广西' },
		{ ename: 'gansu', name: '甘肃' },
		{ ename: 'jin', name: '山西' },
		{ ename: 'neimenggu', name: '内蒙古' },
		{ ename: 'shanxi', name: '陕西' },
		{ ename: 'jilin', name: '吉林' },
		{ ename: 'fujian', name: '福建' },
		{ ename: 'guizhou', name: '贵州' },
		{ ename: 'guangdong', name: '广东' },
		{ ename: 'qinghai', name: '青海' },
		{ ename: 'xizang', name: '西藏' },
		{ ename: 'sichuan', name: '四川' },
		{ ename: 'ningxia', name: '宁夏' },
		{ ename: 'hainan', name: '海南' },
		{ ename: 'taiwan', name: '台湾' },
		{ ename: 'xianggang', name: '香港' },
		{ ename: 'aomen', name: '澳门' },
	],
})

let provinceInfo = reactive([])

let timeOut: any = null // 定时器
let timer: any = null
let carouselPointList = reactive([])
const disableClicking = ref(false)

// onBeforeMount(() => {
//   console.log(timeOut, "---timer--onBeforeMount");
//   setTimeoutFn();
// });
onMounted(() => {
	console.log('-----store.useProjectInfoStore.titleKey:', store.useProjectInfoStore.titleKey)
	allPointList = ChinaData
	getPictureSize()
	// setCarouselPoint()
})

onUnmounted(() => {
	window.removeEventListener('mousemove', debouncedFnAll)
	// clearTimeoutFn()
})

// const proInfo = reactive({
//   disData: store.useProjectInfoStore.displayData,
// })
// watch(() => store.useProjectInfoStore.displayData, (newValue, oldValue) => {
//   proInfo.disData = newValue
// }, { deep: true })
const statePro = reactive({
	proInfo: store.useProjectInfoStore.displayInfoNums,
})
watch(
	() => store.useProjectInfoStore.provinceKey,
	(newValue, oldValue) => {
		const provinceKey = store.useProjectInfoStore.provinceKey
		// if (provinceKey == '全国') {
		// 	isShowBtn.value = false
		// } else {
		// 	isShowBtn.value = true
		// }
		if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
			statePro.proInfo = store.useProjectInfoStore.displayData[provinceKey + 'count']
		} else {
			statePro.proInfo = []
		}
	},
	{ deep: true }
)

/**
 * 鼠标悬停移动事件
 * @param e 事件对象
 */
const handleMouseEvent = (e: any) => {
	let tmpDict = {
		x: e.offsetX / imageWidth.value,
		y: e.offsetY / imageHeight.value,
	}
	const resultKey = checkPointIsInPolygon(tmpDict)
	if (resultKey) {
		// clearTimeoutFn()
		if (resultKey != 'china') {
			debouncedFnIn(resultKey, e)
		} else {
			showFlag.value = false
			promptFlag.value = false
			store.useProjectInfoStore.displayData = {}
			store.useProjectInfoStore.provinceKey = ''
		}
	} else {
		// console.log("---timer--res");
		// !timeOut && setTimeoutFn()
	}
}

const hasResultKey = () => {}

/**
 * 获取默认中国地图-图片的宽度和高度
 */
const getPictureSize = () => {
	nextTick(() => {
		setTimeout(() => {
			let img = document.getElementById('chinaDefaultMap') as HTMLImageElement
			imageWidth.value = img.offsetWidth
			imageHeight.value = img.offsetHeight
		}, 150)
	})
}

/**
 * 测试某点是否在多边形内
 * (平移时使用)
 * */
const checkPointIsInPolygon = (moviedPoint: { x: number; y: number }) => {
	let provinceResKey = ''
	let flag = false
	let checkPoint = [moviedPoint.x, moviedPoint.y]
	allPointList.forEach((item) => {
		for (const key in item) {
			let polygonPoints: any[] = []
			if (Object.prototype.hasOwnProperty.call(item, key)) {
				const itemList = item[key]
				itemList.forEach((element: { x: any; y: any }) => {
					let tmpArray = [element.x, element.y]
					polygonPoints.push(tmpArray)
				})
				if (isInPolygon(checkPoint, polygonPoints)) {
					flag = true
					provinceResKey = key
				}
			}
		}
	})
	return provinceResKey
}

/**
 * 判断某个点是否在多边形区域内
 **/
const isInPolygon = (checkPoint: number[], polygonPoints: string | any[]) => {
	var counter = 0
	var i
	var xinters
	var p1, p2
	var pointCount = polygonPoints.length
	p1 = polygonPoints[0]
	for (i = 1; i <= pointCount; i++) {
		p2 = polygonPoints[i % pointCount]
		if (checkPoint[0] > Math.min(p1[0], p2[0]) && checkPoint[0] <= Math.max(p1[0], p2[0])) {
			if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
				if (p1[0] != p2[0]) {
					xinters = ((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) + p1[1]
					if (p1[1] == p2[1] || checkPoint[1] <= xinters) {
						counter++
					}
				}
			}
		}
		p1 = p2
	}
	if (counter % 2 == 0) {
		return false
	} else {
		return true
	}
}

/**
 * 根据名称获取图片路径
 * @param imageName 图片名称
 */
const getAssetURL = (imageName: string) => {
	return new URL(`../../../assets/img/map/${imageName}.png`, import.meta.url).href
}
/**
 * 返回按钮
 */
// const returnChinaBtn = async () => {
// 	// store.useProjectInfoStore.cityLogoFlag = '0'
// 	// const param = {}
// 	// await store.useProjectInfoStore.getProjectInfoAction(param)
// 	// store.useProjectInfoStore.provinceKey = '全国'
// 	// store.useProjectInfoStore.titleKey = '全国'
// 	window.location.reload()
// 	// router.go(0) //将导航至当前路由。但请注意，这并不会刷新当前页面，它只会模拟历史记录的移动，相当于浏览器的前进或后退
// }
/**
 * 跳转详情
 */
const showProvinceDetail = () => {
	if (!disableClicking.value) {
		store.useProjectInfoStore.cityLogoFlag = '1'
		router.push({
			path: '/province-map',
			query: {
				provinceCode: provinceInfo[0]?.ename,
				provinceName: provinceInfo[0]?.name,
				flag: '0',
			},
			// query: { 'province': provinceKey.value },
		})
		timer && clearInterval(timer)
		timer = null
	}
}

/**
 * 根据code获取省份名称
 * @param code 图片名称
 */
const getProvince = (code: string) => {
	return state.dataList.filter((item) => item.ename == code)
}

/**
 * 通过省份名称设置展示数据
 * @param provinceName
 */
const setDisplayDataByName = (provinceName: string) => {
	let flag = '0' //是否有相同的省份
	store.useProjectInfoStore.displayData = {} // 悬停时，先将缓存中的展示数据置为空
	const cacheData = store.useProjectInfoStore.projectInfo
	for (const key in cacheData) {
		if (cacheData.hasOwnProperty.call(cacheData, key)) {
			store.useProjectInfoStore.titleKey = provinceName
			if (key.includes(provinceName)) {
				store.useProjectInfoStore.displayData[key] = cacheData[key]
				if (!key.includes('count')) {
					flag = '1'
					store.useProjectInfoStore.provinceKey = key
				}
			}
		}
	}
	if (flag == '1') {
		flag = '0'
		//有相同的省份 store.useProjectInfoStore.provinceKey不赋值
	} else {
		store.useProjectInfoStore.provinceKey = provinceName
	}
}

/**
 * 防抖->鼠标频繁悬停切换地图里面和外面时，以最后一次为准
 */
const debouncedFnAll = useDebounceFn((e: any) => {
	handleMouseEvent(e)
}, 100)

/**
 * 防抖->鼠标悬停地图外时，以最后一次为准
 */
const debouncedFnOut = useDebounceFn(() => {
	// const param = {}
	// await store.useProjectInfoStore.getProjectInfoAction(param)
	store.useProjectInfoStore.displayData = store.useProjectInfoStore.projectInfo
	store.useProjectInfoStore.provinceKey = '全国'
	store.useProjectInfoStore.titleKey = '全国'
}, 100)

/**
 * 防抖->鼠标悬停地图中时，以最后一次为准
 */
const debouncedFnIn = useDebounceFn((resultKey: string, e: any) => {
	provinceInfo = getProvince(resultKey)
	setDisplayDataByName(provinceInfo[0]?.name)
	mapHoverUrl.value = getAssetURL(resultKey)

	offsetWidths.value = e.offsetX - 60 // 偏移量需要减去自身宽度的一半 数值越大,越偏左
	if (e.offsetY > 130) {
		// 若偏移量大于自身高度，则显示方向为下的布局
		offsetHeights.value = e.offsetY - 120 // 偏移量需要减去一定的高度
		directionFlag.value = true
	} else {
		offsetHeights.value = e.offsetY + 120 // 偏移量需要增加一定的高度
		directionFlag.value = false
	}
	showFlag.value = true
	// provinceKey.value = resultKey
	promptFlag.value = true
}, 100)

// 定时器
// const setTimeoutFn = () => {
// 	showFlag.value = false
// 	promptFlag.value = false
// 	if (store.useProjectInfoStore.provinceKey != '全国') debouncedFnOut()
// 	if (timeOut || timer) {
// 		clearTimeoutFn()
// 	}
// 	// console.log(timeOut, "-----------in---timer");
// 	timeOut = setTimeout(() => {
// 		disableClicking.value = true
// 		carouselPointFn()
// 	}, 1000 * 24)
// 	// console.log(timeOut, "-----------in---timer-----------");
// }

//点数据
// const setCarouselPoint = () => {
// 	let checkPointList: Array<{ string: number }> = []
// 	provinceRotation.forEach((item) => {
// 		for (const key in item) {
// 			if (Object.prototype.hasOwnProperty.call(item, key)) {
// 				const itemList = item[key]
// 				const data = {
// 					x: itemList[0].x,
// 					y: itemList[0].y,
// 				}
// 				checkPointList.push(data)
// 			}
// 		}
// 	})
// 	carouselPointList = checkPointList
// }

//数据轮播
// const carouselPointFn = () => {
// 	if (carouselPointList && !!carouselPointList?.length) {
// 		let pointIndex: number = 0
// 		if (timer) {
// 			clearInterval(timer)
// 			timer = null
// 		}
// 		timer = setInterval(() => {
// 			// console.log(timeOut, "--timer--------interval");
// 			const carouselPoint = carouselPointList[pointIndex]
// 			setDetailsFn(carouselPoint)
// 			pointIndex++
// 			if (pointIndex === carouselPointList.length) pointIndex = 0
// 		}, 1000 * 6) // 根据需要调整轮播间隔
// 	}
// }
// const setDetailsFn = (tmpDict: any) => {
// 	const resultKey = checkPointIsInPolygon(tmpDict)
// 	let elementPosition = {
// 		offsetX: tmpDict.x * imageWidth.value,
// 		offsetY: tmpDict.y * imageHeight.value,
// 	}
// 	debouncedFnIn(resultKey, elementPosition)
// }

// 清除定时器
// const clearTimeoutFn = () => {
// 	// console.log(timeOut, "-----------timer---clear");
// 	timeOut && clearTimeout(timeOut)
// 	timer && clearInterval(timer)
// 	timeOut = null
// 	timer = null
// 	disableClicking.value = false
// }
</script>
<style scoped lang="scss">
.china-container {
	width: 100%;
	height: 100%;

	.china-map {
		position: relative;
		top: 10px;
		width: 100%;
		height: calc(100% - 10px);
		display: flex;
		align-items: center;

		.showChinaImg {
			width: 770px;
			height: 540px;
			object-fit: contain;
		}

		.hover {
			position: absolute;
			top: 50%;
			left: 0;
			transform: translate(0, -50%);
			// z-index: 10;
			width: 770px;
			height: 540px;
			opacity: 0.6;
			object-fit: contain;
		}

		.anchor-point {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 125px;
			height: 45px;

			.anchor-bg {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 45px;
				color: #9ed7ff;
				font-size: 14px;
				background: url(../../../assets/img/anchor_bg.png) no-repeat;

				&::before {
					content: '';
					display: inline-block;
					margin-right: 9px;
					width: 20px;
					height: 20px;
					background: url(../../../assets/img/anchor_logo.png) no-repeat;
				}

				span {
					color: #f7b500;
				}
			}

			.anchor-line {
				position: absolute;
				top: 45px;
				left: 62.5px;
				width: 1px;
				height: 85px;
				background: linear-gradient(#70b9fe, #025565);
			}
		}

		.anchor-point-reserve {
			position: absolute;
			left: 0;
			top: 130px;
			z-index: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 125px;
			height: 45px;

			.anchor-line-reserve {
				position: absolute;
				bottom: 45px;
				left: 62.5px;
				width: 1px;
				height: 85px;
				background: linear-gradient(#025565, #70b9fe);
			}

			.anchor-bg-reserve {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 45px;
				color: #9ed7ff;
				font-size: 14px;
				background: url(../../../assets/img/anchor_bg.png) no-repeat;

				&::before {
					content: '';
					display: inline-block;
					margin-right: 9px;
					width: 20px;
					height: 20px;
					background: url(../../../assets/img/anchor_logo.png) no-repeat;
				}

				span {
					color: #f7b500;
				}
			}
		}
	}
}

.nanhai {
	position: absolute;
	right: 105px;
	bottom: 66px;
	width: 110px;
	height: 160px;
	background: url(../../../assets/img/<EMAIL>) no-repeat;
	background-size: cover;
}
.returnBtn {
	position: absolute;
	z-index: 1;
	top: 40px;
	right: 40px;
	width: 92px;
	height: 36px;
	background: #384d7e;
	font-family: PingFangSC-Regular;
	border-radius: 4px 4px 4px 4px;
	opacity: 1;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before {
		content: '';
		margin: 0 10px 3px 0;
		display: inline-block;
		width: 20px;
		height: 16px;
		background: url(../../../assets/img/return.png) no-repeat;
	}
}
</style>
