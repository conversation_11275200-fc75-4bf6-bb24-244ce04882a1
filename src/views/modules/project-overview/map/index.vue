<template>
	<div class="container">
		<div class="left">
			<div class="left-title">
				<img
					class="title-img"
					:src="getBGColor(state.cityLogoFlag)" />
				<div class="title">{{ store.useProjectInfoStore.titleKey }}</div>
			</div>
			<div class="pro-list">
				<ul class="pro-info">
					<li class="pro-item">
						<div class="logo1"></div>
						<div class="num">
							<span v-if="store.useProjectInfoStore.titleKey == '全国'">全国重点项目</span>
							<span v-else>本省重点项目</span>
							<span
								class="typeface"
								style="color: #f7b500"
								>{{ state.proInfo?.mainProject }}</span
							>
						</div>
					</li>
					<li class="pro-item">
						<div class="logo2"></div>
						<div class="num">
							<span>未开工项目</span>
							<span
								class="typeface"
								style="color: #168ad1"
								>{{ state.proInfo?.notStart }}</span
							>
						</div>
					</li>
					<li class="pro-item">
						<div class="logo3"></div>
						<div class="num">
							<span>已开工项目</span>
							<span
								class="typeface"
								style="color: #02b65f"
								>{{ state.proInfo?.building }}</span
							>
						</div>
					</li>
					<li class="pro-item">
						<div class="logo4"></div>
						<div class="num">
							<span>已纳管项目</span>
							<span
								class="typeface"
								style="color: #ff8080"
								>{{ state.proInfo?.access }}</span
							>
						</div>
					</li>
					<li class="pro-item">
						<div class="logo4"></div>
						<div class="num">
							<span>已资料管理</span>
							<span
								class="typeface"
								style="color: #ff8080"
								>{{ state.proInfo?.onlyData }}</span
							>
						</div>
					</li>
				</ul>
			</div>
		</div>
		<div class="map">
			<div class="map-main">
				<!-- 默认视图：显示中国地图 -->
				<div v-if="showDefaultView" class="default-map-view">
					<ChinaMap @province-selected="handleProvinceSelected" />
				</div>

				<!-- 中国地图视图 -->
				<div v-if="showChinaMap" class="china-map-view">
					<div class="map-header">
						<!-- <button @click="backToDefault" class="back-btn">返回</button> -->
						<h3>中国地图</h3>
					</div>
					<ChinaMap @province-selected="handleProvinceSelected" />
				</div>

				<!-- 省份地图视图 -->
				<div v-if="showProvinceMap" class="province-map-view">
					<div class="map-header">
						<!-- <button @click="backToDefault" class="back-btn">返回总览</button> -->
						<button @click="backToChinaMap" class="back-btn">返回中国地图</button>
						<h3>{{ props.provinceParams.provinceName }}地图</h3>
					</div>
					<ProvinceMap
						:province-code="props.provinceParams.provinceCode"
						:province-name="props.provinceParams.provinceName"
						:flag="props.provinceParams.flag"
					/>
				</div>
			</div>
		</div>
		<!-- <div class="right"></div> -->
	</div>
</template>
<script setup lang="ts">
import { reactive, watch, onMounted, computed } from 'vue'
import store from '@/store'
import ChinaMap from './ChinaMap.vue'
import ProvinceMap from './ProvinceMap.vue'

// 定义 props
interface Props {
  currentView?: string
  provinceParams?: {
    provinceCode: string
    provinceName: string
    flag: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  currentView: 'default',
  provinceParams: () => ({
    provinceCode: '',
    provinceName: '',
    flag: '0'
  })
})

// 定义 emits
const emit = defineEmits<{
  viewChange: [value: { view: string, params?: any }]
}>()

// 计算当前应该显示的视图
const showDefaultView = computed(() => props.currentView === 'default')
const showChinaMap = computed(() => props.currentView === 'china')
const showProvinceMap = computed(() => props.currentView === 'province')

/**
 * 导航方法
 */
const backToDefault = () => {
  emit('viewChange', { view: 'default' })
}

const backToChinaMap = () => {
  emit('viewChange', { view: 'china' })
}

const handleProvinceSelected = (provinceData: any) => {
  emit('viewChange', {
    view: 'province',
    params: {
      provinceCode: provinceData.provinceCode,
      provinceName: provinceData.provinceName,
      flag: provinceData.flag || '0'
    }
  })
}
onMounted(() => {
	state.cityLogoFlag = store.useProjectInfoStore.cityLogoFlag
	const provinceKey = store.useProjectInfoStore.provinceKey
	if (provinceKey != '') {
		if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
			state.proInfo = store.useProjectInfoStore.displayData[provinceKey + 'count']
		}
	}
})
const state = reactive({
	proInfo: store.useProjectInfoStore.displayInfoNums,
	cityLogoFlag: '',
})
watch(
	() => store.useProjectInfoStore.provinceKey,
	(newValue, oldValue) => {
		const provinceKey = store.useProjectInfoStore.provinceKey
		if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
			state.proInfo = store.useProjectInfoStore.displayData[provinceKey + 'count']
		} else {
			state.proInfo = []
		}
	},
	{ deep: true, flush: 'post' }
)

watch(
	() => store.useProjectInfoStore.cityLogoFlag,
	(newValue, oldValue) => {
		state.cityLogoFlag = store.useProjectInfoStore.cityLogoFlag
		console.log(state.cityLogoFlag)
	},
	{ deep: true, flush: 'post' }
)

/**
 * 根据类型获取背景颜色
 * @param type
 */
const getBGColor = (type: string) => {
	switch (type) {
		case '0':
			return new URL('../../../assets/img/<EMAIL>', import.meta.url).href
		case '1':
			return new URL('../../../assets/img/<EMAIL>', import.meta.url).href
	}
}
</script>

<style lang="scss" scoped>
.container {
	position: relative;
	width: 100%;
	height: 100%;
	background: url(../../../assets/img/map_bg.png) no-repeat;
	display: flex;

	.left {
		position: relative;
		width: 408px;

		.left-title {
			width: 252px;
			margin: 40px 0 0 50px;
			display: flex;

			.title-img {
				width: 20px;
				height: 20px;
				margin-right: 10px;
				// background: url(../../../assets/img/map_logo.png) no-repeat;
				background-repeat: no-repeat;
				background-size: contain;
			}

			.title {
				width: 252px;
				font-size: 20px;
			}

			// .title::before {
			//   width: 20px;
			//   height: 20px;
			//   margin-right: 10px;
			//   vertical-align: middle;
			//   content: url(../../../assets/img/map_logo.png);
			// }
		}

		.pro-list {
			position: absolute;
			left: 50px;
			top: 88px;
			width: 252px;
			height: 484px;
			background: url(../../../assets/img/map_pro_info_bg.png);

			.pro-info {
				display: flex;
				flex-direction: column;
				flex: 4;
				justify-content: space-evenly;
				margin-left: 30px;
				height: 100%;

				.pro-item {
					display: flex;

					.logo1 {
						width: 26px;
						height: 26px;
						background: url(../../../assets/img/map_info_logo_now.png);
					}

					.logo2 {
						width: 25px;
						height: 25px;
						background: url(../../../assets/img/map_info_logo_plan.png);
					}

					.logo3 {
						width: 27px;
						height: 26px;
						background: url(../../../assets/img/map_info_logo_done.png);
					}

					.logo4 {
						width: 27px;
						height: 26px;
						background: url(../../../assets/img/map_info_logo_delay.png);
					}

					.num {
						display: flex;
						flex-direction: column;
						margin-left: 20px;

						span {
							font-size: 16px;
						}

						.typeface {
							margin-top: 12px;
							font-size: 28px;
							font-family: Regular;
						}
					}
				}
			}
		}
	}

	.map {
		flex: 1;
		height: 100%;

		.map-main {
			width: 100%;
			height: 100%;
			position: relative;

			// 默认视图样式
			.default-map-view {
				width: 100%;
				height: 100%;
			}

			// 中国地图和省份地图视图样式
			.china-map-view,
			.province-map-view {
				width: 100%;
				height: 100%;
				position: relative;

				.map-header {
					position: absolute;
					top: 10px;
					left: 10px;
					z-index: 10;
					display: flex;
					align-items: center;
					gap: 10px;

					.back-btn {
						padding: 6px 12px;
						background: rgba(46, 67, 109, 0.9);
						color: white;
						border: none;
						border-radius: 4px;
						cursor: pointer;
						font-size: 12px;
						transition: all 0.3s ease;

						&:hover {
							background: rgba(46, 67, 109, 1);
							transform: translateY(-1px);
						}
					}

					h3 {
						color: #2e436d;
						font-size: 16px;
						font-weight: 600;
						margin: 0;
					}
				}
			}
		}
	}

	// .right {
	//   width: 100px;
	// }
}
</style>
