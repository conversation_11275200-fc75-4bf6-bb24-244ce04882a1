<template>
	<div class="china-container">
		<!-- 中国地图 一级页面 -->

		<div
			class="china-map"
			@mousemove.prevent="debouncedFnAll">
			<!-- <div
				class="returnBtn"
				@click="returnChinaBtn"
				v-show="isShowBtn">
				返回
			</div> -->
			<img
				class="showChinaImg"
				:src="mapUrl"
				alt=""
				id="chinaDefaultMap" />
			<img
				v-if="showFlag"
				class="hover"
				:src="mapHoverUrl"
				alt=""
				@click="showProvinceDetail" />

			<!-- 向上提示框  -->
			<div
				v-show="promptFlag && directionFlag"
				class="anchor-point"
				:style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
				<div class="anchor-bg">
					{{ provinceInfo[0]?.name }}：<span>{{ statePro.proInfo.total >= 0 ? statePro.proInfo.total : 0 }}</span>
				</div>
				<div class="anchor-line"></div>
			</div>
			<!-- 向下提示框  -->
			<div
				v-show="promptFlag && !directionFlag"
				class="anchor-point-reserve"
				:style="{ left: offsetWidths + 'px', top: offsetHeights + 'px' }">
				<div class="anchor-line-reserve"></div>
				<div class="anchor-bg-reserve">
					{{ provinceInfo[0]?.name }}：<span>{{ statePro.proInfo.total >= 0 ? statePro.proInfo.total : 0 }}</span>
				</div>
			</div>
		</div>
	</div>
	<div class="nanhai"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, watch, ref, nextTick } from 'vue'
import { useProjectInfoStore } from '@/store/modules/project'
import ChinaMapDefault from '../../../assets/img/<EMAIL>'
import ChinaData from '../../../assets/json/china.json'
import { useDebounceFn } from '@vueuse/core'

// 定义 emits
const emit = defineEmits<{
  provinceSelected: [value: { provinceCode: string, provinceName: string, flag: string }]
}>()

const projectInfoStore: any = useProjectInfoStore()

let mapUrl = ref(ChinaMapDefault) // 默认中国地图

let mapHoverUrl = ref() // 鼠标悬停时所展示的省份地图

let showFlag = ref(false) // 是否显示省份地图图片

let imageHeight = ref(0) //图片高度

let imageWidth = ref(0) //图片宽度

let actualImageWidth = ref(0) // 图片内容实际显示宽度

let actualImageHeight = ref(0) // 图片内容实际显示高度

let imageOffsetX = ref(0) // 图片内容在元素中的X偏移

let imageOffsetY = ref(0) // 图片内容在元素中的Y偏移

let allPointList: any[] = [] //记录所有区域的路径点

let offsetWidths = ref(0) // 偏移宽度

let offsetHeights = ref(0) //偏移高度

let promptFlag = ref(false) // 提示框标记

let directionFlag = ref(true) // 提示框方向标记（true:上方显示;false:下方显示）

let state = reactive({
	// ename为了获取省份的名字
	dataList: [
		{ ename: 'nanhaizhudao', name: '南海诸岛' },
		{ ename: 'beijing', name: '北京' },
		{ ename: 'tianjin', name: '天津' },
		{ ename: 'shanghai', name: '上海' },
		{ ename: 'chongqing', name: '重庆' },
		{ ename: 'hebei', name: '河北' },
		{ ename: 'henan', name: '河南' },
		{ ename: 'yunnan', name: '云南' },
		{ ename: 'liaoning', name: '辽宁' },
		{ ename: 'heilongjiang', name: '黑龙江' },
		{ ename: 'hunan', name: '湖南' },
		{ ename: 'anhui', name: '安徽' },
		{ ename: 'shandong', name: '山东' },
		{ ename: 'xinjiang', name: '新疆' },
		{ ename: 'jiangsu', name: '江苏' },
		{ ename: 'zhejiang', name: '浙江' },
		{ ename: 'jiangxi', name: '江西' },
		{ ename: 'hubei', name: '湖北' },
		{ ename: 'guangxi', name: '广西' },
		{ ename: 'gansu', name: '甘肃' },
		{ ename: 'jin', name: '山西' },
		{ ename: 'neimenggu', name: '内蒙古' },
		{ ename: 'shanxi', name: '陕西' },
		{ ename: 'jilin', name: '吉林' },
		{ ename: 'fujian', name: '福建' },
		{ ename: 'guizhou', name: '贵州' },
		{ ename: 'guangdong', name: '广东' },
		{ ename: 'qinghai', name: '青海' },
		{ ename: 'xizang', name: '西藏' },
		{ ename: 'sichuan', name: '四川' },
		{ ename: 'ningxia', name: '宁夏' },
		{ ename: 'hainan', name: '海南' },
		{ ename: 'taiwan', name: '台湾' },
		{ ename: 'xianggang', name: '香港' },
		{ ename: 'aomen', name: '澳门' },
	],
})

let provinceInfo = reactive([])

let timer: any = null
const disableClicking = ref(false)

// onBeforeMount(() => {
//   console.log(timeOut, "---timer--onBeforeMount");
//   setTimeoutFn();
// });
onMounted(() => {
	console.log('-----projectInfoStore.titleKey:', projectInfoStore.titleKey)
	allPointList = ChinaData
	getPictureSize()

	// 添加窗口resize监听
	window.addEventListener('resize', handleResize)
	// setCarouselPoint()
})

onUnmounted(() => {
	window.removeEventListener('mousemove', debouncedFnAll)
	window.removeEventListener('resize', handleResize)
	// clearTimeoutFn()
})

/**
 * 窗口尺寸变化处理函数
 */
const handleResize = useDebounceFn(() => {
	getPictureSize()
}, 200)

// const proInfo = reactive({
//   disData: store.useProjectInfoStore.displayData,
// })
// watch(() => store.useProjectInfoStore.displayData, (newValue, oldValue) => {
//   proInfo.disData = newValue
// }, { deep: true })
const statePro = reactive({
	proInfo: projectInfoStore.displayInfoNums,
})
watch(
	() => projectInfoStore.provinceKey,
	() => {
		const provinceKey = projectInfoStore.provinceKey
		// if (provinceKey == '全国') {
		// 	isShowBtn.value = false
		// } else {
		// 	isShowBtn.value = true
		// }
		if (projectInfoStore.displayData.hasOwnProperty(provinceKey)) {
			statePro.proInfo = projectInfoStore.displayData[provinceKey + 'count']
		} else {
			statePro.proInfo = []
		}
	},
	{ deep: true }
)

/**
 * 鼠标悬停移动事件
 * @param e 事件对象
 */
const handleMouseEvent = (e: any) => {
	// 检查图片内容尺寸是否已计算
	if (actualImageWidth.value === 0 || actualImageHeight.value === 0) {
		return
	}

	// 计算鼠标相对于图片内容的坐标
	const mouseX = e.offsetX - imageOffsetX.value
	const mouseY = e.offsetY - imageOffsetY.value

	// 检查鼠标是否在图片内容区域内
	if (mouseX < 0 || mouseX > actualImageWidth.value || mouseY < 0 || mouseY > actualImageHeight.value) {
		// 鼠标在图片内容区域外（空白区域）
		showFlag.value = false
		promptFlag.value = false
		if (projectInfoStore.provinceKey != '全国') {
			debouncedFnOut()
		}
		return
	}

	// 计算相对于图片内容的归一化坐标
	let tmpDict = {
		x: mouseX / actualImageWidth.value,
		y: mouseY / actualImageHeight.value,
	}

	const resultKey = checkPointIsInPolygon(tmpDict)
	if (resultKey) {
		// clearTimeoutFn()
		if (resultKey != 'china') {
			debouncedFnIn(resultKey, e)
		} else {
			showFlag.value = false
			promptFlag.value = false
			projectInfoStore.displayData = {}
			projectInfoStore.provinceKey = ''
		}
	} else {
		showFlag.value = false
		promptFlag.value = false
		if (projectInfoStore.provinceKey != '全国') {
			debouncedFnOut()
		}
		// console.log("---timer--res");
		// !timeOut && setTimeoutFn()
	}
}

/**
 * 获取默认中国地图-图片的宽度和高度
 * 计算图片内容的实际显示尺寸（考虑object-fit: contain的影响）
 */
const getPictureSize = () => {
	nextTick(() => {
		setTimeout(() => {
			let img = document.getElementById('chinaDefaultMap') as HTMLImageElement
			if (!img) return

			// 获取图片元素的显示尺寸
			imageWidth.value = img.offsetWidth
			imageHeight.value = img.offsetHeight

			// 获取图片的原始尺寸
			const naturalWidth = img.naturalWidth
			const naturalHeight = img.naturalHeight

			// 计算图片内容在元素中的实际显示尺寸和偏移
			// object-fit: contain 会保持图片比例，可能在水平或垂直方向留白
			const containerRatio = imageWidth.value / imageHeight.value
			const imageRatio = naturalWidth / naturalHeight

			if (containerRatio > imageRatio) {
				// 容器更宽，图片内容会在垂直方向填满，水平方向居中
				actualImageHeight.value = imageHeight.value
				actualImageWidth.value = imageHeight.value * imageRatio
				imageOffsetX.value = (imageWidth.value - actualImageWidth.value) / 2
				imageOffsetY.value = 0
			} else {
				// 容器更高，图片内容会在水平方向填满，垂直方向居中
				actualImageWidth.value = imageWidth.value
				actualImageHeight.value = imageWidth.value / imageRatio
				imageOffsetX.value = 0
				imageOffsetY.value = (imageHeight.value - actualImageHeight.value) / 2
			}

			console.log('图片尺寸信息:', {
				元素尺寸: { width: imageWidth.value, height: imageHeight.value },
				原始尺寸: { width: naturalWidth, height: naturalHeight },
				内容尺寸: { width: actualImageWidth.value, height: actualImageHeight.value },
				内容偏移: { x: imageOffsetX.value, y: imageOffsetY.value }
			})
		}, 150)
	})
}

/**
 * 测试某点是否在多边形内
 * (平移时使用)
 * */
const checkPointIsInPolygon = (moviedPoint: { x: number; y: number }) => {
	let provinceResKey = ''
	let checkPoint = [moviedPoint.x, moviedPoint.y]
	allPointList.forEach((item) => {
		for (const key in item) {
			let polygonPoints: number[][] = []
			if (Object.prototype.hasOwnProperty.call(item, key)) {
				const itemList = item[key]
				itemList.forEach((element: { x: number; y: number }) => {
					let tmpArray = [element.x, element.y]
					polygonPoints.push(tmpArray)
				})
				if (isInPolygon(checkPoint, polygonPoints)) {
					provinceResKey = key
				}
			}
		}
	})
	return provinceResKey
}

/**
 * 判断某个点是否在多边形区域内
 **/
const isInPolygon = (checkPoint: number[], polygonPoints: number[][]) => {
	let counter = 0
	let i: number
	let xinters: number
	let p1: number[], p2: number[]
	const pointCount = polygonPoints.length
	p1 = polygonPoints[0]
	for (i = 1; i <= pointCount; i++) {
		p2 = polygonPoints[i % pointCount]
		if (checkPoint[0] > Math.min(p1[0], p2[0]) && checkPoint[0] <= Math.max(p1[0], p2[0])) {
			if (checkPoint[1] <= Math.max(p1[1], p2[1])) {
				if (p1[0] != p2[0]) {
					xinters = ((checkPoint[0] - p1[0]) * (p2[1] - p1[1])) / (p2[0] - p1[0]) + p1[1]
					if (p1[1] == p2[1] || checkPoint[1] <= xinters) {
						counter++
					}
				}
			}
		}
		p1 = p2
	}
	return counter % 2 !== 0
}

/**
 * 根据名称获取图片路径
 * @param imageName 图片名称
 */
const getAssetURL = (imageName: string) => {
	return new URL(`../../../assets/img/map/${imageName}.png`, import.meta.url).href
}
/**
 * 返回按钮
 */
// const returnChinaBtn = async () => {
// 	// store.useProjectInfoStore.cityLogoFlag = '0'
// 	// const param = {}
// 	// await store.useProjectInfoStore.getProjectInfoAction(param)
// 	// store.useProjectInfoStore.provinceKey = '全国'
// 	// store.useProjectInfoStore.titleKey = '全国'
// 	window.location.reload()
// 	// router.go(0) //将导航至当前路由。但请注意，这并不会刷新当前页面，它只会模拟历史记录的移动，相当于浏览器的前进或后退
// }
/**
 * 跳转详情 - 改为发射事件给父组件
 */
const showProvinceDetail = () => {
	if (!disableClicking.value && provinceInfo.length > 0) {
		projectInfoStore.cityLogoFlag = '1'

		// 发射省份选择事件给父组件
		emit('provinceSelected', {
			provinceCode: provinceInfo[0]?.ename,
			provinceName: provinceInfo[0]?.name,
			flag: '0'
		})

		timer && clearInterval(timer)
		timer = null
	}
}

/**
 * 根据code获取省份名称
 * @param code 图片名称
 */
const getProvince = (code: string) => {
	return state.dataList.filter((item) => item.ename == code)
}

/**
 * 通过省份名称设置展示数据
 * @param provinceName
 */
const setDisplayDataByName = (provinceName: string) => {
	let flag = '0' //是否有相同的省份
	projectInfoStore.displayData = {} // 悬停时，先将缓存中的展示数据置为空
	const cacheData = projectInfoStore.projectInfo
	for (const key in cacheData) {
		if (cacheData.hasOwnProperty.call(cacheData, key)) {
			projectInfoStore.titleKey = provinceName
			if (key.includes(provinceName)) {
				projectInfoStore.displayData[key] = cacheData[key]
				if (!key.includes('count')) {
					flag = '1'
					projectInfoStore.provinceKey = key
				}
			}
		}
	}
	if (flag == '1') {
		flag = '0'
		//有相同的省份 projectInfoStore.provinceKey不赋值
	} else {
		projectInfoStore.provinceKey = provinceName
	}
}

/**
 * 防抖->鼠标频繁悬停切换地图里面和外面时，以最后一次为准
 */
const debouncedFnAll = useDebounceFn((e: any) => {
	handleMouseEvent(e)
}, 100)

/**
 * 防抖->鼠标悬停地图外时，以最后一次为准
 */
const debouncedFnOut = useDebounceFn(() => {
	// const param = {}
	// await projectInfoStore.getProjectInfoAction(param)
	projectInfoStore.displayData = projectInfoStore.projectInfo
	projectInfoStore.provinceKey = '全国'
	projectInfoStore.titleKey = '全国'
}, 100)

/**
 * 防抖->鼠标悬停地图中时，以最后一次为准
 */
const debouncedFnIn = useDebounceFn((resultKey: string, e: any) => {
	Object.assign(provinceInfo, getProvince(resultKey))
	setDisplayDataByName(provinceInfo[0]?.name)
	mapHoverUrl.value = getAssetURL(resultKey)

	// 计算标记区域的位置，考虑图片内容的实际偏移
	const tooltipWidth = 125 // 提示框宽度
	const tooltipHeight = 45 // 提示框高度
	const lineHeight = 85 // 连接线高度

	// 基于原始鼠标位置计算，但确保在图片内容区域内
	offsetWidths.value = e.offsetX - tooltipWidth / 2 // 居中显示

	// 判断提示框显示方向，基于上方是否有足够空间
	const mouseY = e.offsetY
	const totalTooltipHeight = tooltipHeight + lineHeight + 20 // 提示框总高度 + 安全边距
	const spaceAbove = mouseY // 上方可用空间

	if (spaceAbove >= totalTooltipHeight) {
		// 上方空间足够，提示框显示在上方
		offsetHeights.value = mouseY - tooltipHeight - lineHeight
		directionFlag.value = true // 使用anchor-point（提示框在上方）
	} else {
		// 上方空间不足，提示框显示在下方
		offsetHeights.value = mouseY + lineHeight
		directionFlag.value = false // 使用anchor-point-reserve（提示框在下方）
	}

	// 确保提示框不超出容器边界
	if (offsetWidths.value < 0) {
		offsetWidths.value = 10
	} else if (offsetWidths.value + tooltipWidth > imageWidth.value) {
		offsetWidths.value = imageWidth.value - tooltipWidth - 10
	}

	showFlag.value = true
	// provinceKey.value = resultKey
	promptFlag.value = true
}, 100)

// 定时器
// const setTimeoutFn = () => {
// 	showFlag.value = false
// 	promptFlag.value = false
// 	if (store.useProjectInfoStore.provinceKey != '全国') debouncedFnOut()
// 	if (timeOut || timer) {
// 		clearTimeoutFn()
// 	}
// 	// console.log(timeOut, "-----------in---timer");
// 	timeOut = setTimeout(() => {
// 		disableClicking.value = true
// 		carouselPointFn()
// 	}, 1000 * 24)
// 	// console.log(timeOut, "-----------in---timer-----------");
// }

//点数据
// const setCarouselPoint = () => {
// 	let checkPointList: Array<{ string: number }> = []
// 	provinceRotation.forEach((item) => {
// 		for (const key in item) {
// 			if (Object.prototype.hasOwnProperty.call(item, key)) {
// 				const itemList = item[key]
// 				const data = {
// 					x: itemList[0].x,
// 					y: itemList[0].y,
// 				}
// 				checkPointList.push(data)
// 			}
// 		}
// 	})
// 	carouselPointList = checkPointList
// }

//数据轮播
// const carouselPointFn = () => {
// 	if (carouselPointList && !!carouselPointList?.length) {
// 		let pointIndex: number = 0
// 		if (timer) {
// 			clearInterval(timer)
// 			timer = null
// 		}
// 		timer = setInterval(() => {
// 			// console.log(timeOut, "--timer--------interval");
// 			const carouselPoint = carouselPointList[pointIndex]
// 			setDetailsFn(carouselPoint)
// 			pointIndex++
// 			if (pointIndex === carouselPointList.length) pointIndex = 0
// 		}, 1000 * 6) // 根据需要调整轮播间隔
// 	}
// }
// const setDetailsFn = (tmpDict: any) => {
// 	const resultKey = checkPointIsInPolygon(tmpDict)
// 	let elementPosition = {
// 		offsetX: tmpDict.x * imageWidth.value,
// 		offsetY: tmpDict.y * imageHeight.value,
// 	}
// 	debouncedFnIn(resultKey, elementPosition)
// }

// 清除定时器
// const clearTimeoutFn = () => {
// 	// console.log(timeOut, "-----------timer---clear");
// 	timeOut && clearTimeout(timeOut)
// 	timer && clearInterval(timer)
// 	timeOut = null
// 	timer = null
// 	disableClicking.value = false
// }
</script>
<style scoped lang="scss">
.china-container {
	width: 100%;
	height: 100%;

	.china-map {
		position: relative;
		top: 10px;
		width: 100%;
		height: calc(100% - 10px);
		display: flex;
		align-items: center;

		.showChinaImg {
			width: 770px;
			height: 540px;
			object-fit: contain;
		}

		.hover {
			position: absolute;
			top: 50%;
			left: 0;
			transform: translate(0, -50%);
			// z-index: 10;
			width: 770px;
			height: 540px;
			opacity: 0.6;
			object-fit: contain;
		}

		.anchor-point {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 125px;
			height: 45px;

			.anchor-bg {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 45px;
				color: #9ed7ff;
				font-size: 14px;
				background: url(../../../assets/img/anchor_bg.png) no-repeat;

				&::before {
					content: '';
					display: inline-block;
					margin-right: 9px;
					width: 20px;
					height: 20px;
					background: url(../../../assets/img/anchor_logo.png) no-repeat;
				}

				span {
					color: #f7b500;
				}
			}

			.anchor-line {
				position: absolute;
				top: 45px;
				left: 62.5px;
				width: 1px;
				height: 85px;
				background: linear-gradient(#70b9fe, #025565);
			}
		}

		.anchor-point-reserve {
			position: absolute;
			left: 0;
			top: 130px;
			z-index: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 125px;
			height: 45px;

			.anchor-line-reserve {
				position: absolute;
				bottom: 45px;
				left: 62.5px;
				width: 1px;
				height: 85px;
				background: linear-gradient(#025565, #70b9fe);
			}

			.anchor-bg-reserve {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 45px;
				color: #9ed7ff;
				font-size: 14px;
				background: url(../../../assets/img/anchor_bg.png) no-repeat;

				&::before {
					content: '';
					display: inline-block;
					margin-right: 9px;
					width: 20px;
					height: 20px;
					background: url(../../../assets/img/anchor_logo.png) no-repeat;
				}

				span {
					color: #f7b500;
				}
			}
		}
	}
}

.nanhai {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 110px;
	height: 160px;
	background: url(../../../assets/img/<EMAIL>) no-repeat;
	background-size: cover;
}
.returnBtn {
	position: absolute;
	z-index: 1;
	top: 40px;
	right: 40px;
	width: 92px;
	height: 36px;
	background: #384d7e;
	font-family: PingFangSC-Regular;
	border-radius: 4px 4px 4px 4px;
	opacity: 1;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;

	&::before {
		content: '';
		margin: 0 10px 3px 0;
		display: inline-block;
		width: 20px;
		height: 16px;
		background: url(../../../assets/img/return.png) no-repeat;
	}
}
</style>
