<template>
	<div class="container">
		<slot></slot>
		<el-scrollbar
			class="scroll"
			ref="scrollArea"
			@mouseenter="stopScroll"
			@mouseleave="startScroll">
			<div
				v-for="(item, index) in state.eventList"
				:key="index">
				<div class="com">
					<div
						class="com-img"
						:style="{ background: getBGColor(item?.type) }">
						{{ item?.title }}
					</div>
					<div class="com-name">{{ item?.shortName }}</div>
					<div class="com-date">
						{{ moment(item?.pushTime).format('YYYY.MM.DD') }}
					</div>
				</div>
				<div class="com-bottom">
					<div class="com-bottom-img"></div>
					<div class="com-event">{{ item?.content }}</div>
				</div>
			</div>
		</el-scrollbar>
	</div>
</template>
<script setup lang="ts">
import { reactive, watch, onBeforeMount, ref, onUnmounted, onMounted } from 'vue'
import store from '@/store'
import moment from 'moment'
import { queryGreatEventDataApi } from '@/script/api/common/commomApi'

const state = reactive({
	eventList: [],
})
const scrollArea: any = ref(null)

onBeforeMount(() => {
	let projectId = ''
	state.eventList = []
	const provinceKey = store.useProjectInfoStore.provinceKey
	console.log('store.useProjectInfoStore.displayData: ', store.useProjectInfoStore.displayData)
	if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
		store.useProjectInfoStore.displayData[provinceKey].forEach((element) => {
			projectId += element.projectId + ','
		})
		queryGreatEventData(projectId)
	}
})

onMounted(() => {
	startScroll()
})

onUnmounted(() => {
	stopScroll()
})

// 监控key值,获取数字档案数据
watch(
	() => store.useProjectInfoStore.displayData,
	(newval, oldval) => {
		let projectId = ''
		const provinceKey = store.useProjectInfoStore.provinceKey
		console.log('store.useProjectInfoStore.displayData: ', store.useProjectInfoStore.displayData)
		if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
			store.useProjectInfoStore.displayData[provinceKey].forEach((element) => {
				projectId += element.projectId + ','
			})
			queryGreatEventData(projectId)
		} else {
			projectId = ''
			state.eventList = []
		}
	},
	{ deep: true }
)

/**
 * 获取重要事件信息
 * @param projectId
 */
const queryGreatEventData = async (projectId: string) => {
	const params = {
		projectId,
	}
	const { data } = await queryGreatEventDataApi(params)
	state.eventList = data
	console.log('data: ', data)
}

/**
 * 根据类型获取背景颜色
 * @param type
 */
const getBGColor = (type: string) => {
	let bgColor = ''
	switch (type) {
		case '0': // 重要观摩
			bgColor = 'linear-gradient(90deg, #0055FF 0%, #25A4FF 100%)'
			break
		case '1': // 处罚公告
			bgColor = 'linear-gradient(90deg, #FF3C3C 0%, #FF5A5A 100%)'
			break
		case '2': // 进度里程碑
			bgColor = 'linear-gradient(90deg, #11AA56 3%, #4FB84B 100%)'
			break
		default:
			bgColor = 'linear-gradient(90deg, #0055FF 0%, #25A4FF 100%)'
			break
	}
	return bgColor
}

let timer: any = null
let timeout: any = null
const SCROLL_SPEED = 1 // 每次滚动的像素数，可以根据需要调整
const startScroll = () => {
	if (timer) {
		clearInterval(timer)
	}
	timer = setInterval(() => {
		const container = scrollArea.value.$el.querySelector('.el-scrollbar__wrap')
		// 判断是否已滚动到底部
		if (container && container.scrollHeight - (container.scrollTop + container.clientHeight) <= 1) {
			timeout = setTimeout(() => {
				container.scrollTop = 0
			}, 500)
		} else {
			container.scrollTop += SCROLL_SPEED
		}
	}, 30) // 根据需要调整滚动间隔
}
const stopScroll = () => {
	if (timer) {
		clearInterval(timer)
		clearTimeout(timeout)
	}
}
</script>
<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.head {
		width: 100%;
		height: 63px;
		background: url(../../../assets/img/event_bg.png);
		background-repeat: no-repeat;
		background-size: contain;
	}

	.scroll {
		padding-bottom: 16px;
		width: 100%;
		height: 100%;
		padding-left: 28px;

		.com {
			margin-right: 24px;
			display: flex;
			align-items: center;
			margin-top: 27px;

			.com-img {
				// width: 56px;
				padding: 4px;
				height: 20px;
				font-size: 12px;
				align-items: center;
				display: flex;
				justify-content: center;
				font-weight: 500;
				color: #ffffff;
				line-height: 12px;
				font-family: PingFangSC-Medium;
				opacity: 1;
				border-radius: 6% 6% 0% 6%;
				white-space: nowrap;
			}

			.com-img1 {
				width: 56px;
				height: 20px;
				font-size: 12px;
				align-items: center;
				display: flex;
				justify-content: center;
				font-weight: 500;
				color: #ffffff;
				font-family: PingFangSC-Medium;
				line-height: 12px;
				background: linear-gradient(90deg, #ff3c3c 0%, #ff5a5a 100%);
				opacity: 1;
				border-radius: 6% 6% 0% 6%;
			}

			.com-img2 {
				width: 56px;
				height: 20px;
				font-size: 12px;
				align-items: center;
				display: flex;
				justify-content: center;
				font-weight: 500;
				color: #ffffff;
				font-family: PingFangSC-Medium;
				line-height: 12px;
				background: linear-gradient(90deg, #11aa56 3%, #4fb84b 100%);
				opacity: 1;
				border-radius: 6% 6% 0% 6%;
			}

			.com-name {
				margin-left: 10px;
				font-size: 14px;
				font-family: PingFangSC-Medium;
				font-weight: 500;
				color: #ffffff;
				line-height: 14px;
			}

			.com-date {
				font-size: 14px;
				font-family: PingFangSC-Regular;
				font-weight: 400;
				color: #ffffff;
				line-height: 14px;
				margin: auto;
				margin-right: 0px;
				opacity: 0.7;
			}
		}

		.com-bottom {
			margin-top: 11px;
			margin-right: 24px;
			display: flex;
			align-items: center;

			.com-bottom-img {
				width: 6px;
				height: 6px;
				background: #d8d8d8;
				opacity: 0.7;
				border-radius: 100%;
				margin-left: 66px;
			}

			.com-event {
				font-size: 14px;
				font-family: PingFangSC-Regular;
				font-weight: 400;
				color: #ffffff;
				line-height: 14px;
				opacity: 0.7;
				margin-left: 4px;
			}
		}
	}
}
</style>
