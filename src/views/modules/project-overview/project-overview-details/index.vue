<template>
  <div class="mask-detail">
    <div class="dialog-pro-details" :style="{ transform: `translate(-50%, -50%) scale(${dialogScale})` }">
      <div class="container-ps">
        <div class="return-btn" @click="bigReturn"></div>

        <div class="ps-header">
        </div>
        <div class="ps-top">
          <div class="img">
            <div v-if="projectInfo.infos?.collect == 0" class="pro-type"></div>
            <img v-if="projectInfo.infos?.images" :src="projectInfo.infos?.images" @error="imgError">
            <img v-else src="../../../assets/img/logo_big.png">
          </div>
          <div class="pro-right">
            <div class="pro-heard">
              <div class="pro-img"></div>
              <div class="pro-name">{{ projectInfo.infos?.shortName }}</div>
              <div class="continue-btn" @click="openExternal(projectInfo.infos?.accAddress)">进入</div>
            </div>
            <div class="second-line">
              <div class="second-left">
                <div class="current-img"></div>
                <div class="current">当前阶段</div>
                <div class="current-stage">{{ projectInfo.infos?.currentStage }}</div>
              </div>
              <div class="second-right">
                <div class="current-img"></div>
                <div class="current">当前状态</div>
                <div class="current-stage" :style="{ color: getBGColor(projectInfo.infos?.delayLevel) }">{{
                  getInsFromType(projectInfo.infos?.delayLevel) }}</div>
              </div>

            </div>

            <div class="fourth-line">
              <div class="fourth-left">
                <div>建筑面积：<span>{{ projectInfo.infos?.bulitUpArea }}㎡</span></div>
                <div>开工日期：<span>{{ moment(projectInfo.infos?.startDate).format('YYYY年MM月DD日') }}</span></div>
                <div>竣工日期：<span>{{ moment(projectInfo.infos?.endDate).format('YYYY年MM月DD日') }}</span></div>
              </div>
              <div class="fourth-right">
                <div>设计单位：<span>{{ projectInfo.infos?.designUnit }}</span></div>
                <div>建设单位：<span>{{ projectInfo.infos?.constructionUnit }}</span></div>
                <div>监理单位：<span>{{ projectInfo.infos?.controlUnit }}</span> </div>
                <div>施工单位：<span>{{ projectInfo.infos?.totalUnit }}</span> </div>
                <!-- <div>勘察单位：<span>{{ projectInfo.infos?.controlUnit }}</span></div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="ps-center">
          <div class="second-right">
            <div class="stage-img"></div>
            <div class="current">施工进度</div>
            <div class="current-stage">{{ projectInfo.infos?.isAble == '0' ? progressData : projectInfo.infos?.pace }}%</div>
          </div>
          <div class="third-row">
            <div class="time-container">
              <div :style="{ 'width': item.pace + '%' }" class="time"
                v-for="(item, index) in projectInfo.infos?.progress">
                <div class="end-of" v-if="index + 1 == projectInfo.infos?.progress.length && item.pace == 0"><span>{{
                  item.progressDate }} </span>|</div>
                <div v-else>|<span>{{ item.progressDate }} </span></div>
              </div>
            </div>

            <div class="demo-progress">
              <el-progress :percentage="progressDisplayData" :show-text="false" />
            </div>
            <div class="stage-container">
              <div :style="{ 'width': itemName.pace + '%' }" class="stage"
                v-for="(itemName, indexName) in projectInfo.infos?.progress">
                <div class="text-stage" v-if="indexName + 1 == projectInfo.infos?.progress.length && itemName.pace == 0">
                  {{
                    itemName.progressName }}</div>
                <div v-else>{{ itemName.progressName }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="ps-bottom">
          <div class="bm-same">
            <CameraStatusMonitor :project-id="props.projectId">
              <div class="echarts-ai-header">摄像头状态监控</div>
            </CameraStatusMonitor>
          </div>
          <div class="bm-same">
            <Labor :project-id="props.projectId">
              <div class="echarts-ai-header">劳务考勤分析 [今日]</div>
            </Labor>
          </div>
          <div class="bm-same">
            <EchartsPieDetails :project-id="props.projectId">
              <div class="echarts-ai-header">AI预警分析 [今日]</div>
            </EchartsPieDetails>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import EchartsPieDetails from "../../components/EchartsPieDetails.vue";
import CameraStatusMonitor from "../../direct-scence/camera-statusMonitor/CameraStatusDetails.vue";
import Labor from "../../project-overview/labor/index.vue";
import { reactive, ref, onBeforeMount, onUnmounted } from "vue";
import { queryProjectDetailApi } from '@/script/api/common/commomApi'
import moment from 'moment'

// 响应式缩放组合函数
const useResponsiveScale = (baseWidth = 1388, baseHeight = 925) => {
  const scale = ref(1);

  const calculateScale = () => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算基于宽度和高度的缩放比例
    const scaleX = (viewportWidth * 0.95) / baseWidth; // 95%的视口宽度
    const scaleY = (viewportHeight * 0.90) / baseHeight; // 90%的视口高度

    // 取较小的缩放比例，确保弹窗完全可见
    let newScale = Math.min(scaleX, scaleY);

    // 设置最小和最大缩放限制
    newScale = Math.max(0.3, Math.min(1.5, newScale));

    scale.value = newScale;
  };

  const handleResize = () => {
    calculateScale();
  };

  const startListening = () => {
    calculateScale();
    window.addEventListener('resize', handleResize);
  };

  const stopListening = () => {
    window.removeEventListener('resize', handleResize);
  };

  return {
    scale,
    startListening,
    stopListening,
    calculateScale
  };
};

const props = defineProps<{
  //子组件接收父组件传递过来的值
  projectId: String
}>()


let projectInfo = reactive({
  infos: {},
})

let progressData = ref(0)
let progressDisplayData = ref(0)

// 初始化响应式缩放
const { scale: dialogScale, startListening, stopListening } = useResponsiveScale(1388, 925);

onBeforeMount(async () => {
  const params = {
    "platform": "1",
    "projectId": props.projectId
  }
  const { data } = await queryProjectDetailApi(params)
  projectInfo.infos = data
  getProgressData()
  getProgressDisplayData()
  // 启动响应式缩放监听
  startListening();
})

onUnmounted(() => {
  // 停止响应式缩放监听
  stopListening();
})

const emit = defineEmits(['showDetailsInfo'])

const bigReturn = () => {
  emit('showDetailsInfo', false)
}

//计算日期间隔天数
const getDiffDay = (date_1: string, date_2: string) => {
  // 计算两个日期之间的差值
  let totalDays, diffDate
  let myDate_1 = Date.parse(date_1)
  let myDate_2 = Date.parse(date_2)
  // 将两个日期都转换为毫秒格式，然后做差
  diffDate = Math.abs(myDate_1 - myDate_2) // 取相差毫秒数的绝对值
  totalDays = Math.floor(diffDate / (1000 * 3600 * 24)) // 向下取整
  return totalDays // 相差的天数
}

/**
 * 通过类型获取描述
 * @param type 类型
 */
const getInsFromType = (type: string) => {
  console.log('type: ', type);
  switch (type) {
    case '0': // 正常
      return '正常'
    case '1': // 延期
      return '延期'
    case '2': // 严重延期
      return '严重延期'
    case '3': // 未纳管
      return '未纳管'
  }
}
/**
 * 通过类型获取颜色
 * @param type 类型
 */
const getBGColor = (type: string) => {
  switch (type) {
    case '0': // 正常
      return '#02B65F'
    case '1': // 延期
      return '#FFBB00'
    case '2': // 严重延期
      return '#FF3535'
    case '3': // 未纳管
      return '#6989A4'
  }
}
/**
 * 打开外部链接
 * @param url
 */
const openExternal = (url: string) => {
  if (url) {
    window.open(url, '_blank')
  }
}

/**
 * 图片加载失败时，执行此方法
 */
const imgError = () => {
  projectInfo.infos.images = new URL('../../../assets/img/logo_big.png', import.meta.url).href
}

/**
 * 获取项目总进度
 */
const getProgressData = () => {
  let nowDate = moment(new Date()).format('YYYY-MM-DD');
  let startDate = null
  let endDate = null
  let lengthObje = projectInfo.infos?.progress.length
  for (let i = 0; i < projectInfo.infos?.progress.length; i++) {
    startDate = projectInfo.infos?.progress[0].progressDate
    endDate = projectInfo.infos?.progress[lengthObje - 1].progressDate
  }
  if(moment().isAfter(endDate)) {
    progressData.value = 100
  }else {
    const total = getDiffDay(startDate, endDate) as number
    const nowTotal = getDiffDay(startDate, nowDate) as number
    progressData.value = ((nowTotal / total) * 100).toFixed(2)
  }
}

/**
 * 获取项目进度条展示总进度
 */
const getProgressDisplayData = () => {
  let nowDate = moment(new Date()).format('YYYY-MM-DD') // 当前日期
  let startDate = null // 距离当前日期最近的开始日期
  let endDate = null // 距离当前日期最近的结束日期
  let nowNumber = 0 // 距离当前日期最近的开始日期所在的索引
  let lengthObje = projectInfo.infos?.progress.length
  let finalDate = projectInfo.infos?.progress[lengthObje-1].progressDate
  if(moment().isAfter(finalDate)){
    progressDisplayData.value = 100
  }else {
    for (let i = 0; i < lengthObje; i++) {
      if (i < lengthObje - 1) {
        startDate = projectInfo.infos?.progress[i].progressDate
        endDate = projectInfo.infos?.progress[i + 1].progressDate
        if (moment().isAfter(startDate) && moment().isBefore(endDate)) { // 判断当前日期是否处于两个时间之内
          console.log(projectInfo.infos?.progress[i].progressDate)
          nowNumber = i
          break
        }
      }
    }
    console.log('nowNumber: ', nowNumber);
    if (nowNumber >= 0) {
      const total = getDiffDay(startDate, endDate)
      const nowTotal = getDiffDay(startDate, nowDate)
      const currentPro = (nowTotal / total).toFixed(2)
      let disData = 0
      const resultCurr = projectInfo.infos?.progress[nowNumber].pace * currentPro
      console.log('resultCurr: ', resultCurr);
      for (let j = 0; j < nowNumber; j++) {
        disData += projectInfo.infos?.progress[j].pace
      }
      progressDisplayData.value = resultCurr + disData
    }
  }
}

</script>
<style lang="scss" scoped>
@mixin echarts-header {
  width: 100%;
  height: 40px;
}

@mixin border-same {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1), rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)) 1 1;
}

/*透明蒙层遮罩层*/
.mask-detail {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: rgba(234, 234, 234, 0.2);
  backdrop-filter: blur(20px); // 遮罩蒙层虚化
}

.dialog-pro-details {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1388px;
  height: 925px;
  transition: transform 0.3s ease-in-out;
  transform-origin: center center;
}

.container-ps {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;
  background: #182137;
  border-radius: 8px;

  .return-btn {
    position: absolute;
    // z-index: 100;
    top: -35px;
    width: 20px;
    right: 0px;
    height: 20px;
    background: url(../../../assets/img/back.png);

  }



  .ps-header {
    height: 12px;
    margin-top: 32px;
    background: url(../../../assets/img/<EMAIL>) no-repeat;
    background-size: contain;
    margin-left: 30px;
    margin-right: 35px;
  }

  .ps-top {
    width: 100%;
    height: 422px;
    margin-top: 30px;
    display: flex;

    .img {
      width: 718px;
      height: 402px;
      background: #D8D8D8;
      border-radius: 4px 4px 4px 4px;
      opacity: 1;
      margin-left: 30px;
      position: relative;

      .pro-type {
        top: 20px;
        left: 20px;
        position: absolute;
        width: 136px;
        height: 28px;
        background: url(../../../assets/img/<EMAIL>) no-repeat;
        background-size: contain;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .pro-right {
      flex: 1;
      height: 100%;
      margin-left: 30px;
      // background: #666;
      margin-right: 35px;
      flex-direction: column;
    }

    .pro-heard {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-top: 4px;

      .pro-img {
        width: 22px;
        height: 22px;
        background: url('../../../assets/img/<EMAIL>') no-repeat;
        background-size: contain;
      }

      .pro-name {
        font-size: 20px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        margin-left: 6px;
        line-height: 20px;

      }

      .continue-btn {
        width: 67px;
        height: 26px;
        background: #135AFF;
        border-radius: 60px 60px 60px 60px;
        opacity: 1;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        margin-left: 10px;
        line-height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;

        &::before {
          content: '';
          margin: 0 5px 0px 0;
          display: inline-block;
          width: 14px;
          height: 14px;
          background: url(../../../assets/img/<EMAIL>) no-repeat;
          background-size: contain;
        }
      }
    }

    .second-line {
      display: flex;
      margin-top: 32px;
      width: 100%;
      height: 64px;

      .second-left {
        width: 285px;
        height: 64px;
        align-items: center;
        justify-content: center;
        background: #1B253C;
        border-radius: 4px 4px 4px 4px;
        display: flex;

        .current-img {
          width: 20px;
          height: 20px;
          background: url(../../../assets/img/<EMAIL>) no-repeat;
          background-size: contain;
          margin-right: 10px;
        }

        .current {
          margin-top: 3px;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 14px;
        }

        .current-stage {
          margin-top: 3px;
          font-size: 14px;
          font-weight: 400;
          color: #0086C7;
          line-height: 14px;
          margin-left: 12px;
        }
      }

      .second-right {
        width: 285px;
        height: 100%;
        margin-left: 20px;
        align-items: center;
        justify-content: center;
        background: #1B253C;
        border-radius: 4px 4px 4px 4px;
        display: flex;

        .current-img {
          width: 20px;
          height: 20px;
          background: url(../../../assets/img/<EMAIL>) no-repeat;
          background-size: contain;
          margin-right: 10px;
        }


        .current {
          margin-top: 3px;
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 14px;
        }

        .current-stage {
          margin-top: 3px;
          font-size: 14px;
          font-weight: 400;
          color: #02B65F;
          line-height: 14px;
          margin-left: 12px;
        }
      }

    }



    .fourth-line {
      display: flex;
      width: 100%;
      height: 263px;
      background: #1B253C;
      border-radius: 4px 4px 4px 4px;
      margin-top: 20px;
      flex-direction: column;
      padding: 20px;

      span {
        color: #FFFFFF;
      }

      .fourth-left {
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #6989A4;
        line-height: 24px;
        flex: 1;
      }

      .fourth-right {
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #6989A4;
        line-height: 24px;
        flex: 1;
      }
    }
  }

  .ps-center {
    display: flex;
    margin-right: 35px;
    margin-left: 30px;
    height: 100px;
    margin-bottom: 20px;

    .second-right {
      width: 190px;
      height: 100%;
      align-items: center;
      justify-content: center;
      background: #1B253C;
      border-radius: 4px 4px 4px 4px;
      display: flex;
      position: relative;

      .stage-img {
        position: absolute;
        width: 14px;
        height: 14px;
        background: url(../../../assets/img/<EMAIL>) no-repeat;
        background-size: contain;
        top: 25px;
        left: 26px;
      }

      .current {
        top: 25px;
        left: 45px;
        position: absolute;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 14px;
      }

      .current-stage {
        position: absolute;
        right: 38px;
        bottom: 25px;
        font-size: 24px;
        font-family: Regular;
        font-weight: 400;
        color: #02B65F;
        line-height: 24px;
      }
    }

    .third-row {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      height: 100px;
      background: #1B253C;
      border-radius: 2px 2px 2px 2px;


      .time-container {
        display: flex;
        width: 100%;
        padding: 0px 16px 0px 16px;
        height: 16px;
        position: relative;

        .time {
          width: 100%;
          color: #6989A4;

          .end-of {
            position: absolute;
            right: 15px;
            top: 0;
            // font-size: 10px;
            // font-family: PingFang SC-Regular, PingFang SC;
            // font-weight: 400;
            // color: #6989A4;
            // padding-right: 2px;
            // line-height: 10px;
          }

          span {
            font-size: 10px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #6989A4;
            margin-left: 2px;
            line-height: 10px;
          }
        }
      }

      .stage-container {
        display: flex;
        height: 12px;
        padding: 0px 16px 0px 16px;
        width: 100%;
        margin-top: 12px;
        position: relative;

        .stage {

          // display: flex;
          width: 100%;
          font-size: 10px;
          left: 0%;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: 10px;

          .text-stage {
            position: absolute;
            top: 0;
            right: 15px;
          }
        }
      }

      .demo-progress {
        margin-top: 4px;
        width: 100%;
        height: 8px;
        padding: 0px 16px 0px 16px;

        /* 渐变进度条 */
        :deep(.el-progress-bar__inner) {
          height: 8px;
          background-color: unset;
          background-image: linear-gradient(270deg, #FFCD43 0%, rgba(27, 37, 60, 0) 100%);
          // background-image: linear-gradient(to right, #3587d8 , #6855ff);
          border-radius: 0px 0px 0px 0px;
        }



        :deep(.el-progress-bar__outer) {
          height: 8px;
          background-color: #252E44;
          border-radius: 0px 0px 0px 0px;
        }
      }

    }
  }

  .ps-bottom {
    width: 100%;
    height: 278px;
    display: flex;
    padding: 0px 30px 0px 25px;


    .bm-same {
      flex: 1;
      margin: 0px 5px 0px 5px;
      height: 278px;
      background: rgba(26, 36, 59);
      opacity: 1;
      border-radius: 4px 4px 4px 4px;

    }

    .echarts-ai-header {
      @include echarts-header;
      align-items: center;
      display: flex;
      padding-left: 34px;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: #DAEAFF;
      background: url(../../../assets/img/<EMAIL>) no-repeat;
      background-size: contain;
    }
  }
}</style>