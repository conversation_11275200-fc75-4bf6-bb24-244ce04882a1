<template>
  <div class="container">
    <div v-if="showBingFlag" class="mask">
      <div class="dialog-pro" :style="{ transform: `translate(-50%, -50%) scale(${dialogScale})` }">
        <ProjectSchedule @show-po-info="showPOInfo"> </ProjectSchedule>
      </div>
    </div>
    <div v-if="showMapFlag">
      <ProjectOverviewDetails
        :project-id="itemProjectId"
        @show-details-info="showDetailsInfo"
      ></ProjectOverviewDetails>
    </div>
    <div>
      <div class="middle">
        <div class="left">
          <ProgressWarning
            @show-big-info="showBigInfo"
            @show-list-info="showListInfo"
          ></ProgressWarning>
        </div>
        <div class="right">
          <Map
            :current-view="mapViewControl.currentView"
            :province-params="mapViewControl.provinceParams"
            @view-change="handleMapViewChange"
          ></Map>
        </div>
      </div>
      <div class="bottom">
        <div class="camera">
          <EchartsStatistics>
            <div class="echarts-statistics-header"></div>
          </EchartsStatistics>
          <!-- <CameraStatusMonitor /> -->
        </div>
        <div class="bm-same">
          <ImportantEvents>
            <div class="echarts-events-header"></div>
          </ImportantEvents>
          <!-- <EchartsLaborServices id="0">
            <div class="echarts-gz-header"></div>
          </EchartsLaborServices> -->
        </div>
        <div class="bm-same">
          <DigitalArchives id="2" :list="state.aiList"></DigitalArchives>
        </div>
        <div class="bm-same">
          <AiWarning></AiWarning>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Map from "./map/index.vue";
import ProjectOverviewDetails from "./project-overview-details/index.vue";
import ProgressWarning from "./progress-warning/index.vue";
import EchartsStatistics from "../components/EchartsStatistics.vue";
import ImportantEvents from "./important-events/index.vue";
import ProjectSchedule from "./project-schedule/index.vue";
import DigitalArchives from "./digital-archives/index.vue";
import AiWarning from "./ai-warning/index.vue";
import { reactive, onMounted, ref, onUnmounted } from "vue";
import store from "@/store";

// 响应式缩放组合函数
const useResponsiveScale = (baseWidth = 1810, baseHeight = 958) => {
  const scale = ref(1);

  const calculateScale = () => {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算基于宽度和高度的缩放比例
    const scaleX = (viewportWidth * 0.95) / baseWidth; // 95%的视口宽度
    const scaleY = (viewportHeight * 0.90) / baseHeight; // 90%的视口高度

    // 取较小的缩放比例，确保弹窗完全可见
    let newScale = Math.min(scaleX, scaleY);

    // 设置最小和最大缩放限制
    newScale = Math.max(0.3, Math.min(1.5, newScale));

    scale.value = newScale;
  };

  const handleResize = () => {
    calculateScale();
  };

  const startListening = () => {
    calculateScale();
    window.addEventListener('resize', handleResize);
  };

  const stopListening = () => {
    window.removeEventListener('resize', handleResize);
  };

  return {
    scale,
    startListening,
    stopListening,
    calculateScale
  };
};
const state = reactive({
  projectId: "",
  aiList: [],
  dataList: [
    { ename: "nanhaizhudao", name: "南海诸岛" },
    { ename: "beijing", name: "北京" },
    { ename: "tianjin", name: "天津" },
    { ename: "shanghai", name: "上海" },
    { ename: "chongqing", name: "重庆" },
    { ename: "hebei", name: "河北" },
    { ename: "henan", name: "河南" },
    { ename: "yunnan", name: "云南" },
    { ename: "liaoning", name: "辽宁" },
    { ename: "heilongjiang", name: "黑龙江" },
    { ename: "hunan", name: "湖南" },
    { ename: "anhui", name: "安徽" },
    { ename: "shandong", name: "山东" },
    { ename: "xinjiang", name: "新疆" },
    { ename: "jiangsu", name: "江苏" },
    { ename: "zhejiang", name: "浙江" },
    { ename: "jiangxi", name: "江西" },
    { ename: "hubei", name: "湖北" },
    { ename: "guangxi", name: "广西" },
    { ename: "gansu", name: "甘肃" },
    { ename: "jin", name: "山西" },
    { ename: "neimenggu", name: "内蒙古" },
    { ename: "shanxi", name: "陕西" },
    { ename: "jilin", name: "吉林" },
    { ename: "fujian", name: "福建" },
    { ename: "guizhou", name: "贵州" },
    { ename: "guangdong", name: "广东" },
    { ename: "qinghai", name: "青海" },
    { ename: "xizang", name: "西藏" },
    { ename: "sichuan", name: "四川" },
    { ename: "ningxia", name: "宁夏" },
    { ename: "hainan", name: "海南" },
    { ename: "taiwan", name: "台湾" },
    { ename: "xianggang", name: "香港" },
    { ename: "aomen", name: "澳门" }
  ]
});
const param = {
  platform: "1"
};

let showBingFlag = ref(false);
let showMapFlag = ref(false);
let itemProjectId = ref("");

// 初始化响应式缩放
const { scale: dialogScale, startListening, stopListening } = useResponsiveScale(1810, 958);

// 地图视图控制状态
const mapViewControl = reactive({
  currentView: "default", // 'default' | 'china' | 'province'
  provinceParams: {
    provinceCode: "",
    provinceName: "",
    flag: "0"
  }
});

/**
 * 处理地图视图变化事件
 * @param viewData 视图数据
 */
const handleMapViewChange = (viewData: { view: string; params?: any }) => {
  mapViewControl.currentView = viewData.view;
  if (viewData.params) {
    mapViewControl.provinceParams = viewData.params;
  }
};

onMounted(async () => {
  await store.useProjectInfoStore.getProjectInfoAction(param);
  getProjectInfo();
  // 启动响应式缩放监听
  startListening();
});

onUnmounted(() => {
  // 停止响应式缩放监听
  stopListening();
});
const getProjectInfo = () => {
  if (store.useProjectInfoStore.displayData.length != 0) {
    if (store.useProjectInfoStore.displayData.hasOwnProperty("全国")) {
      store.useProjectInfoStore.provinceKey = "全国";
      store.useProjectInfoStore.titleKey = "全国";
      store.useProjectInfoStore.cityLogoFlag = "0";
      // 切换到中国地图视图
      mapViewControl.currentView = "china";
    } else {
      for (const key in store.useProjectInfoStore.displayData) {
        if (
          store.useProjectInfoStore.displayData.hasOwnProperty.call(
            store.useProjectInfoStore.displayData,
            key
          )
        ) {
          if (!key.includes("count")) {
            store.useProjectInfoStore.provinceKey = key;
            break;
          }
        }
      }
      let ename = "";
      let name = "";
      state.dataList.forEach(element => {
        if (store.useProjectInfoStore.provinceKey.includes(element.name)) {
          store.useProjectInfoStore.titleKey = element.name;
          ename = element.ename;
          name = element.name;
          return;
        }
      });
      store.useProjectInfoStore.cityLogoFlag = "1";
      // 切换到省份地图视图
      mapViewControl.currentView = "province";
      mapViewControl.provinceParams = {
        provinceCode: ename,
        provinceName: name,
        flag: "1"
      };
    }
  }
};
/**
 * 放大按钮子组件回调方法
 * @param val
 */
const showBigInfo = (val: boolean) => {
  showBingFlag.value = val;
};

/**
 * 大图详情页子组件回调方法
 * @param val
 */
const showPOInfo = (val: boolean) => {
  showBingFlag.value = val;
};
/**
 * 项目列表子组件回调方法
 * @param val
 */
const showListInfo = (val: any) => {
  console.log("val: ", val);
  itemProjectId.value = val.projectId;
  showMapFlag.value = true;
};
/**
 * 项目详情子组件回调方法
 * @param val
 */
const showDetailsInfo = (val: boolean) => {
  showMapFlag.value = val;
};
</script>

<style lang="scss" scoped>
@mixin border-same {
  border-radius: 4px 4px 4px 4px;
  border: 1px solid;
  border-image: linear-gradient(
      180deg,
      rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1),
      rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)
    )
    1 1;
}

@mixin echarts-header {
  width: 100%;
  height: 50px;
}

/*透明蒙层遮罩层*/

.container {
  width: 100%;
  height: 100%;
  margin: 0 !important;
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background: rgba(234, 234, 234, 0.3);
    backdrop-filter: blur(20px); // 遮罩蒙层虚化

    .dialog-pro {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 1810px;
      height: 958px;
      transition: transform 0.3s ease-in-out;
      transform-origin: center center;
    }
  }

  // .dialog-pro-details{
  //   position: absolute;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%, -50%);
  //   width: 1388px;
  //   height: 925px;
  // }

  .middle {
    width: 100vw;
    height: 616px;
    display: flex;

    .left {
      margin: 15px 5px 5px 16px;
      width: 520px;
      height: 616px;
      opacity: 1;
      @include border-same;
    }

    .right {
      flex: 1;
      margin: 15px 16px 5px 5px;
      height: 616px;
      opacity: 1;
      @include border-same;
    }
  }

  .bottom {
    margin-top: 20px;
    width: 100vw;
    height: 300px;
    display: flex;

    .camera {
      margin: 5px 5px 0 16px;
      width: 520px;
      height: 300px;
      opacity: 1;
      @include border-same;

      .echarts-statistics-header {
        @include echarts-header;
        background: url(../../assets/img/statistics_title_bg.png) no-repeat;
      }
    }

    .bm-same {
      flex: 1;
      margin: 5px 5px 0 5px;
      height: 300px;
      opacity: 1;
      @include border-same;

      .echarts-events-header {
        width: 100%;
        height: 63px;
        background: url(../../assets/img/event_bg.png) no-repeat;
      }

      .echarts-gz-header {
        @include echarts-header;
        background: url(../../assets/img/laowu_title_bg.png);
      }

      .echarts-ai-header {
        @include echarts-header;
        background: url(../../assets/img/ai_title_bg.png);
      }
    }

    .bm-same:last-child {
      margin: 5px 16px 0 5px;
    }
  }
}
</style>
