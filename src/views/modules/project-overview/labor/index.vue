
<template>
  <slot></slot>
  <div class="main">
    <div class="count">
      <div>
        <p class="number">{{ state.today }}</p>
        <p>今日出勤</p>
      </div>
      <div>
        <p class="number">{{ state.allCount }}</p>
        <p>在场人数</p>
      </div>
    </div>
    <div class="echarts-labor-main" ref="labor" id="labor"></div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from "echarts"
import { onMounted, ref, onBeforeMount, onBeforeUnmount, reactive } from 'vue'
import Socket from '@/websocket'
const props = defineProps<{
  //子组件接收父组件传递过来的值
  projectId?: string
}>()
const state = reactive({
  allCount: 0,
  today: 0,
  xList: [],
  yList: []
})
let wbSocket = null;
onBeforeMount(async () => {
  pushAiMessage(props.projectId)
})
const pushAiMessage = (val: string) => {
  if (wbSocket) {
    wbSocket.destroy()
  }

  wbSocket = new Socket({ url: import.meta.env.VITE_BASE_LABOR + val })
  wbSocket.onmessage((data: RT) => {
    state.xList = []
    state.yList = []
    state.allCount = data.allCount
    state.today = data.today
    console.log(data);
    data?.list.forEach(element => {
      for (const key in element) {
        if (Object.prototype.hasOwnProperty.call(element, key)) {
          state.xList.push(key)
          state.yList.push(element[key])
        }
      }
    });
    renderChart()
  })
  wbSocket.options.heartMsg = { projectId: val }

}
const renderChart = () => {
  var chartDom = document.getElementById('labor') as HTMLElement;
  let myChart = echarts.getInstanceByDom(chartDom)
  if (myChart == null) {

    myChart = echarts.init(chartDom);

  }
  var option;

  option = {
    color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      show: false
    },
    grid: {
      left: '2%',
      right: '10%',
      bottom: '10%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisTick: { //x轴刻度尺
        show: false
      },
      data: state.xList,
      axisLine: {//x轴线条颜色
        lineStyle: {
          color: '#2C62A4'
        }
      },
      axisLabel: {
        show: true,
        color: '#6989A4'
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {//网格线
        show: true, //关闭网格线
        // 或者
        lineStyle: {
          type: 'solid',    //设置网格线类型 dotted：虚线   solid:实线
          color: '#23354A'  //网格线颜色
        },
      },
      axisLine: {//y轴线条颜色
        show: false,
        lineStyle: {
          color: '#999'
        }
      },
      axisLabel: {//y轴文字的配置
        color: "#6989A4",//Y轴内容文字颜色
      },
    },

    series: [{
      type: 'line',
      stack: 'Total',
      smooth: true,
      lineStyle: {
        width: 1,
        color: '#24B2FF'
      },
      showSymbol: false,
      areaStyle: {
        opacity: 1,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 1,
            color: 'rgba(106,213,255,0)'

          },
          {
            offset: 0.001,
            color: 'rgba(86,197,255,0.1904)'
          },
          {
            offset: 0,
            color: '#0184FF'
          }
        ])
      },
      emphasis: {
        focus: 'series'
      },
      data: state.yList
    }
    ]
  };

  option && myChart.setOption(option);
};
onMounted(() => {
  renderChart()
})
onBeforeUnmount(async () => {
  if (wbSocket) {
    wbSocket.destroy()
  }
})
</script>
<style lang="scss" scoped>
.echarts-labor-header {
  width: 100%;
  height: 50px;
  background: url(../../../assets/img/kaoqin_title_bg.png);
}

.main {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;

  .count {
    margin-top: 20px;
    width: 152px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;


    div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
      width: 84px;
      height: 84px;

      &:nth-child(1) {
        background: url(../../../assets/img/<EMAIL>) no-repeat;
        background-size: contain;
      }

      &:nth-child(2) {
        background: url(../../../assets/img/<EMAIL>) no-repeat;
        background-size: contain;

      }

      .number {
        margin: -10px 0px 0px 0px;
        font-size: 18px;
        color: #fff;
        font-family: Regular;
      }

      p {
        margin: 5px 0px 0px 0px;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 12px;
        font-weight: 500;
      }
    }
  }

  .echarts-labor-main {
    flex: 1;
    height: 100%;
  }
}
</style>
