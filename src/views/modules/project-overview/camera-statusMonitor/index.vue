<template>
    <div class="container">
        <div class="head" />
        <div class="bottom">
            <div class="bottom-left" :list="state.list">
                <div v-for="(item, index) in state.list" :key="index">
                    <div class="list-parent" v-show="index < 3">
                        <div class="list-index">
                            <img class="list-img" v-if="index === 0" src="../../../assets/img/qiangji.png" alt="" />
                            <img class="list-img" v-if="index === 1" src="../../../assets/img/qiuji.png" alt="" />
                            <img class="list-img" v-if="index === 2" src="../../../assets/img/quanjing.png" alt="" />
                            <div class="line"></div>
                            <span class="list-number">{{ item.onlinePercent }}%</span>
                        </div>
                        <div class="shape">
                            <dv-percent-pond class="progress" :config="item.configs" />
                        </div>
                    </div>

                </div>
            </div>
            <div class="number-states">
                <div class="img-total">
                    <span class="total-data">{{ state.camerasTotal }}</span>
                    <span class="total">总数</span>
                </div>
                <div class="img-offline">
                    <span class="offline-data">{{ state.offLine }}</span>
                    <span class="offline">离线</span>
                </div>
            </div>
            <el-scrollbar :list="state.projectPoint" class="bottom-right">
                <div v-for="(item, index) in state.projectPoint" :key="index">
                    <div class="list-location-parent">
                        <div class="list-location-index">
                            <div class="list-img-id">[{{ index + 1 }}]</div>
                            <span class="list-name" :title="item.projectName">{{ item.projectName }}</span>
                        </div>
                        <div class="demo-progress">
                            <el-progress :percentage="Number(item.onlinePercent)" :show-text="false"
                                :color="customColors" />
                        </div>
                    </div>

                </div>
            </el-scrollbar>

        </div>

    </div>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted, watch } from "vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { getPointDataByProjectId } from '../../../../script/api/common/commomApi';
import store from '@/store'
const state = reactive({
    camerasTotal: "",
    offLine: "",
    list: [],
    projectPoint: [],
    provinceKeycount: store.useProjectInfoStore.displayInfoNums,
    projectId: "",
})
watch(() => store.useProjectInfoStore.provinceKey, (newValue, oldValue) => {
    const provinceKey = store.useProjectInfoStore.provinceKey
    console.log(provinceKey);
    if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
        state.projectId = ""
        store.useProjectInfoStore.displayData[provinceKey].forEach(element => {
            state.projectId = state.projectId + element.projectId + ","
        });
        getPointData(state.projectId)
    }
    else {
        state.projectId = ""
        state.camerasTotal = '0'
        state.offLine = '0'
        state.list = []
        state.projectPoint = []
    }
}, { deep: true, flush: 'post' });
onMounted(() => {
    const provinceKey = store.useProjectInfoStore.provinceKey
    if (provinceKey != '') {
        if (store.useProjectInfoStore.displayData.hasOwnProperty(provinceKey)) {
            state.projectId = ""
            store.useProjectInfoStore.displayData[provinceKey].forEach(element => {
                state.projectId = state.projectId + element.projectId + ","
            });
            getPointData(state.projectId)
        }
    }
});
const getPointData = (val: string) => {
    const param = {
        'platform': '1',
        'projectId': val
    }
    getPointDataByProjectId(param).then((res) => {
        if (res.data != null) {

            state.camerasTotal = res.data.total
            state.offLine = res.data.offLine
            state.list = res.data.point;
            state.projectPoint = res.data.projectPoint
            //创建新对象
            state.list.forEach(element => {
                config.value = Number(element.onlinePercent);

                if (element.pointType == "0") {
                    config.colors = ["#172684 20%", "#59AFFF"]
                } else if (element.pointType == "1") {
                    config.colors = ["#1E9F9B 20%", "#1E9F9B"]
                } else if (element.pointType == "3") {
                    config.colors = ["#8A6100 0%", "#B89E57"]
                }
                //，对象拷贝
                element.configs = Object.assign({}, config);
            });
        }
    })
}
const config = reactive({
    value: '66',
    localGradient: false,
    textColor: "#00000000",
    borderRadius: 0,
    borderGap: 0,
    borderWidth: 0,
    lineDash: [2, 3],
    colors: ["#338A6100", "#B89E57"],
})
const customColors = [
    { color: '#FF5858', percentage: 40 },
    { color: '#D4A421', percentage: 80 },
    { color: '#18989C', percentage: 100 },
]
</script>
<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .head {
        width: 100%;
        height: 62px;
        background: url(../../../assets/img/camera_title_bg.png);
    }

    .bottom {
        width: 100%;
        height: 100%;
        display: flex;
        padding-left: 24px;

        .bottom-left {
            margin-top: 14px;
            width: 130px;
            height: 225px;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            .list-parent {
                width: 100%;
                height: 44px;
                display: flex;
                flex-direction: column;
                margin-top: 27px;

                .list-index {
                    position: relative;
                    width: 100%;
                    display: flex;
                    height: 14px;
                    align-items: center;
                    margin-bottom: 10px;

                    .list-img {
                        width: 51px;
                        height: 14px;

                    }

                    .line {
                        width: 44px;
                        height: 1px;
                        border-radius: 0px 0px 0px 0px;
                        opacity: 1;
                        border: 1px solid;
                        border-image: linear-gradient(135deg, rgba(2.000000118277967, 206.53332710266113, 238.00000101327896, 0), rgba(63.88432279229164, 94.94439572095871, 124.46428209543228, 1)) 1 1;
                    }

                    .list-number {
                        position: absolute;
                        right: 0;
                        height: 12px;
                        font-size: 12px;
                        font-family: DIN-MediumItalic-Regular, DIN-MediumItalic;
                        font-weight: 500;
                        color: #F6FAFF;
                        margin-left: 6px;
                        line-height: 12px;
                    }
                }
            }

            .shape {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 20px;
                width: 130px;
                border: 2px solid;
                border-radius: 1px;
                border-color: #25416F;
                margin-top: 8dp;

                .progress {
                    width: 130px;
                    height: 10px;
                }
            }
        }


        .number-states {
            width: 121px;
            height: 100%;
            padding: 0 22px;
            justify-content: center;
            margin-top: 41px;

            .img-total {
                width: 76px;
                height: 76px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                background: url(../../../assets/img/camera_num_bg.png);

                .total-data {
                    font-size: 16px;
                    font-family: OPPOSans-Bold, OPPOSans;
                    font-weight: 700;
                    color: #FFFFFF;
                    line-height: 16px;
                }

                .total {
                    margin-top: 4px;
                    width: 28px;
                    height: 20px;
                    font-size: 14px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #517CBE;
                    line-height: 20px;
                }
            }

            .img-offline {
                justify-content: center;
                align-content: center;
                margin-top: 30px;
                width: 76px;
                height: 76px;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                display: flex;
                background: url(../../../assets/img/camera_num_bg.png);

                .offline-data {
                    font-size: 16px;
                    font-family: OPPOSans-Bold, OPPOSans;
                    font-weight: 700;
                    color: #F7B500;
                    line-height: 16px;
                }

                .offline {
                    margin-top: 4px;
                    width: 28px;
                    height: 20px;
                    font-size: 14px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: #517CBE;
                    line-height: 20px;

                }
            }


        }

        .bottom-right {
            margin-top: 17px;
            width: 244px;
            height: 225px;
            display: flex;
            flex-direction: column;
            margin-bottom: 17;

            .list-location-parent {
                width: 100%;
                display: flex;
                flex-direction: column;
                margin-top: 24px;

                .list-location-index {
                    width: 100%;
                    display: flex;

                    .list-img-id {
                        height: 17px;
                        font-size: 12px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #6989A4;
                        line-height: 17px;
                    }

                    .list-name {
                        width: 200px; //设置宽度
                        overflow: hidden; //溢出隐藏
                        text-overflow: ellipsis; //属性值表示当对象内文本溢出时显示省略标记，省略标记插入的位置是最后一个字符。
                        white-space: nowrap; //只保留一个空白，文本不会换行，会在在同一行上继续，直到遇到br标签为止。
                        align-self: flex-start;
                        font-size: 12px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        margin-left: 8px;
                        color: #6989A4;
                        line-height: 17px;
                    }
                }

                .demo-progress {
                    margin-top: 8px;
                    width: 220px;
                    height: 2px;

                    /* 渐变进度条 */
                    :deep(.el-progress-bar__inner) {
                        width: 220px;
                        height: 2px;
                        background-color: unset;
                        // background-image: linear-gradient(82deg, rgba(30, 38, 64, 0) 0%, #0084FF 83%, #FFFFFF 100%);

                    }

                    :deep(.el-progress-bar__outer) {
                        width: 220px;
                        height: 2px !important;
                        background-color: #252E44;
                    }
                }

            }
        }

    }

}
</style>