<template>
	<div class="container-archive">
		<div class="projectList">
			<CompanyList :collectMapList="state.collectMapList" @node-click="CompanyListClickFn"></CompanyList>
			<div class="bomBtn_r_top">
				<router-link v-if="state.isRouterShow === '3'"
				:to="`/bank1?projectId=${state.projectId}&shortName=${state.shortName}`" target="_blank">
				<div class="bomBtn_r_bank1">全国管理员后台</div>
			</router-link>
			<div class="bomBtn_l_all" v-if="state.isRouterShow === '3'" @click="downloadFn1">
					<img src="../../assets/img/archive/CM_1.png" alt="">
					<p>全国导出</p>
				</div>
			</div>
			
		</div>
		<div class="catalogModule" :style="`width: ${state.isRightIcon ? 320 : 520}px;`">
			<CatalogModule @catalog-module="CatalogModuleClickFn" v-show="state.isCompanyListClick"
				:parentData="state.parentData"></CatalogModule>
			<div class="centerDiv" v-show="!state.isCompanyListClick">请选择项目</div>
			<div class="rightIcon" @click="isRightIconFn">
				<img v-show="state.isRightIcon" src="../../assets/img/archive/CM_3.png" alt="">
				<img v-show="!state.isRightIcon" src="../../assets/img/archive/CM_4.png" alt="">
			</div>
			<div class="bomBtn" v-show="state.isCompanyListClick">
				<div class="bomBtn_l" v-if="state.isType >= 1" @click="downloadFn">
					<img src="../../assets/img/archive/CM_1.png" alt="">
					<p>导出</p>
				</div>
				<div class="bomBtn_r" v-if="state.isType >= 2">
					<router-link :to="`/bank?projectId=${state.projectId}&shortName=${state.shortName}`"
						target="_blank">
						<div class="bomBtn_r">项目资料管理员后台</div>
					</router-link>
				</div>
			</div>
		</div>
		<div class="dataModule" :style="`width: ${state.isRightIcon ? 1017 : 817}px;`">
			<dataModule v-if="state.tableWidth" :tableData="state.tableData" :pathData="state.pathData"
				@clear-PathData="clearPathDataFn" v-show="state.isCompanyListClick"
				@search-change="queryDataDirectoryFn" @size-Change="sizeChangeFn" :isType="state.isType"
				@current-Change="currentChangeFn" :pageObj="state.pageObj" />
			<div class="centerDiv" v-show="!state.isCompanyListClick">请选择项目</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { reactive, onBeforeMount, ref, defineEmits, watch, nextTick } from 'vue'
import CompanyList from './company/CompanyList.vue'
import CatalogModule from './catalog-module/index.vue'
import dataModule from './data-Module/index.vue'
import { useRouter } from 'vue-router';
import { queryCompanyFilesProject, queryDataDirectory, exportPdf,adminExportPdf, queryPageDataDirectory, queryPersonPermissions } from '@/script/api/common/commomApi'
const router = useRouter();
const state = reactive({
	isRouterShow: '',
	isRightIcon: true,
	parentData: [],
	isCompanyListClick: false,
	tableWidth: true,
	pathData: [],
	tableData: [],
	collectMapList: [],
	projectId: '',
	shortName: '',
	isType: 0,
	pageObj: {
		pageSize: 10,
		pageNo: 1,
		total: 1000,
	},
	paramsFn: ''

})
onBeforeMount(async () => {
	init();
	queryPersonPermissionsFn();
})
const init = async () => {
	const params = {
		"platform": "1"
	}
	const { data } = await queryCompanyFilesProject(params)
	state.collectMapList = data.collectMap;
	console.log(state.collectMapList, 'state.collectMapcollectMap');


}
const sizeChangeFn = (e: any) => {
	state.pageObj.pageSize = e
	queryPageDataDirectoryFn(state.paramsFn)
}
const currentChangeFn = (e: any) => {
	state.pageObj.pageNo = e
	queryPageDataDirectoryFn(state.paramsFn)
}
const queryPageDataDirectoryFn = async (params: any) => {
	params.pageNo = state.pageObj.pageNo
	params.pageSize = state.pageObj.pageSize
	const resTable = await queryPageDataDirectory(params)

	state.tableData = resTable.data.list
	state.pageObj.total = resTable.data.total
}
const queryPersonPermissionsFn = async () => {
	const resTable = await queryPersonPermissions({})
	state.isRouterShow = resTable.data.type

}

// 请求列表数据
const queryDataDirectoryFn = async (datas: any = null, num: any) => {
	let params = { "projectId": state.projectId }

	if (datas !== null) { params[datas.label] = datas.val }
	state.paramsFn = params
	const res = await queryDataDirectory(params)

	if (res.status === 0) {
		state.isType = Number(res.data.type ? res.data.type : 0)
		console.log(res, 'resssssssssssssssssssss');
		if (num === 'first') {
			state.parentData = res.data.directoryVOList
		}
		queryPageDataDirectoryFn(params)
	} else {
		ElMessage.error('请求失败')
	}
}
const downloadFn = async () => {

	const baseData = await exportPdf(state.projectId, state.shortName)
	const fileName = baseData.data.fileName
	let binaryString = window.atob(baseData.data.file)
	let binaryLen = binaryString.length
	let bytes = new Uint8Array(binaryLen)
	for (var i = 0; i < binaryLen; i++) {
		bytes[i] = binaryString.charCodeAt(i)
	}
	let excelBlob = new Blob([bytes], {
		type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	})
	let a = document.createElement('a')
	a.href = window.URL.createObjectURL(excelBlob)
	a.download = fileName
	document.body.appendChild(a)
	a.click()
	document.body.removeChild(a)

}

const downloadFn1 = async () => {

const baseData = await adminExportPdf()
const fileName = baseData.data.fileName
let binaryString = window.atob(baseData.data.file)
let binaryLen = binaryString.length
let bytes = new Uint8Array(binaryLen)
for (var i = 0; i < binaryLen; i++) {
	bytes[i] = binaryString.charCodeAt(i)
}
let excelBlob = new Blob([bytes], {
	type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
})
let a = document.createElement('a')
a.href = window.URL.createObjectURL(excelBlob)
a.download = fileName
document.body.appendChild(a)
a.click()
document.body.removeChild(a)

}
// 左一点击事件
const CompanyListClickFn = async (datas: any) => {
	state.isCompanyListClick = true
	state.projectId = datas.projectId
	state.shortName = datas.shortName
	console.log(datas, 'datasdatasdatasdatas');
	state.pageObj.pageNo = 1
	queryDataDirectoryFn(null, 'first')
	clearPathDataFn([])
}
// 左二点击事件
const CatalogModuleClickFn = (e: any, node: any) => {
	state.pageObj.pageNo = 1
	if (node.level === 1) {
		queryDataDirectoryFn({ label: 'firstDirectoryId', val: e.firstDirectoryId })
		state.pathData = [{ label: e.firstDirectoryName, idName: 'firstDirectoryId', id: e.firstDirectoryId }]
	} else {
		state.pathData = [
			{
				label: node.parent.data.firstDirectoryName,
				idName: 'firstDirectoryId',
				id: node.parent.data.firstDirectoryId
			},
			{
				label: e.secondDirectoryName,
				idName: 'secondDirectoryId',
				id: e.secondDirectoryId,
				secondDescribe: e.secondDescribe
			}]
		queryDataDirectoryFn({ label: 'secondDirectoryId', val: e.secondDirectoryId })
	}

}
// 面包屑点击
const clearPathDataFn = (arr: any) => {
	state.pathData = arr
	if (arr.length > 0) {
		queryDataDirectoryFn({ label: arr[arr.length - 1].idName, val: arr[arr.length - 1].id })
	} else {
		queryDataDirectoryFn(null)
	}

}
// 左二模块放大缩小
const isRightIconFn = () => {
	state.tableWidth = false;
	state.isRightIcon = !state.isRightIcon
	setTimeout(() => {
		state.tableWidth = true;
	})

}

</script>

<style lang="scss" scoped>
@mixin border-same {
	border-radius: 4px 4px 4px 4px;
	border: 1px solid;
	border-image: linear-gradient(180deg,
			rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1),
			rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)) 1 1;
}

@mixin flexCenter {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

:deep(.el-scrollbar__thumb) {
	background-color: #0C417F;
	opacity: 1;
	height: 10px!important;
}

.container-archive {
	width: 100%;
	height: 100%;
	display: flex;
	margin: 0 !important;
	.centerDiv {
		line-height: 926px;
		font-size: 20px;
		text-align: center;
	}

	.projectList {
		margin: 15px 5px 5px 16px;
		width: 520px;
		height: 926px;
		padding-bottom: 50px;
		opacity: 1;
		position: relative;
		@include border-same;
	}

	.catalogModule {
		margin: 15px 0px 0px 16px;
		height: 926px;
		position: relative;
		padding-bottom: 50px;
		padding-right: 20px;
		border-right: 1px solid;
		border-top: 1px solid;
		border-image: linear-gradient(180deg,
				rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1),
				rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)) 1 1;

		.rightIcon {
			cursor: pointer;
			width: 36px;
			position: absolute;
			right: -18px;
			top: 40%;

			img {
				width: 100%;
			}
		}

		.bomBtn {
			width: 100%;
			padding-right: 18px;
			position: absolute;
			bottom: 0;
			right: 0;
			font-size: 20px;
			@include flexCenter;

			.bomBtn_l {
				cursor: pointer;
				@include flexCenter;

				img {
					width: 38px;
				}
			}

			.bomBtn_r {
				cursor: pointer;
				color: #D3D6DD;
			}
		}
	}

	.bomBtn_r_top {
		margin-top: 20px;
		padding-left: 41px;
		
		font-size: 20px;
		display: flex;
		align-items: center;
		.bomBtn_r_bank1{
			cursor: pointer;
			color: #D3D6DD;
			margin-right: 100px;
		}
		.bomBtn_l_all{
			cursor: pointer;
		color: #D3D6DD;
			display: flex;
		align-items: center;
		justify-content: space-between;
		}
	}
	

	.dataModule {
		margin-top: 15px;
		margin-left: 26px;
		// flex-grow: 1;

		height: 926px;
		position: relative;

	}
}
</style>