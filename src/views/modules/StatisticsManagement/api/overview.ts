import { http } from '@/utils/http';

// 服务名配置 - 方便后续更换服务名
const SERVICE_NAME = 'api';

// API返回的原始数据结构
export interface StatisticsOverviewApiResponse {
  total: number;
  provinceCount: number;
  jcCount: number;
}

// 总览数据接口 - 组件内部使用的数据结构
export interface OverviewData {
  totalProjects: number;    // 对应API返回的 total
  procurementProjects: number; // 对应API返回的 jcCount
  uncompletedProjects: number; // 计算得出：total - jcCount
  provinceCount?: number;   // 对应API返回的 provinceCount（可选，用于扩展）
}

// 获取总览数据 - 使用真实接口
export const getStatisticsOverviewData = () => {
  return http.get<any, RestResult<StatisticsOverviewApiResponse>>(`/${SERVICE_NAME}/materials/overview`);
};

// 数据转换函数：将API返回的数据转换为组件需要的格式
export const transformOverviewData = (apiData: StatisticsOverviewApiResponse | null): OverviewData => {
  if (!apiData) {
    // 当接口报错或返回为空时，默认所有值都是0
    return {
      totalProjects: 0,
      procurementProjects: 0,
      uncompletedProjects: 0,
      provinceCount: 0
    };
  }

  return {
    totalProjects: apiData.total || 0,
    procurementProjects: apiData.jcCount || 0,
    uncompletedProjects: (apiData.total || 0) - (apiData.jcCount || 0),
    provinceCount: apiData.provinceCount || 0
  };
};
