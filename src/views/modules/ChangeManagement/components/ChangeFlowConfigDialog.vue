<template>
  <el-dialog
    v-model="visible"
    title="变更流转配置"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="config-container">
      <!-- 左侧省份树 -->
      <div class="province-tree">
        <div class="tree-header">
          <!-- <span class="header-title">变更流转配置</span> -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            size="small"
            clearable
            style="width: 150px; margin-top: 8px;"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

        </div>
        <div class="tree-content">
          <el-tree
            ref="provinceTreeRef"
            :data="provinceTreeData"
            :props="treeProps"
            node-key="code"
            :expand-on-click-node="false"
            highlight-current
            :default-expanded-keys="['all']"
            :default-checked-keys="['all']"
            @current-change="handleTreeNodeClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node-content">
                <svg
                  style="margin-right: 8px"
                  viewBox="0 0 16 16"
                  width="16"
                  height="16"
                >
                  <path
                    :d="`${
                      data.isLeaf
                        ? 'M13,6 L9,6 L9,5 L9,2 L3,2 L3,14 L13,14 L13,6 Z M12.5857864,5 L10,2.41421356 L10,5 L12.5857864,5 Z M2,1 L10,1 L14,5 L14,15 L2,15 L2,1 Z'
                        : 'M16,6 L14,14 L2,14 L0,6 L16,6 Z M14.7192236,7 L1.28077641,7 L2.78077641,13 L13.2192236,13 L14.7192236,7 Z M6,1 L8,3 L15,3 L15,5 L14,5 L14,4 L7.58578644,4 L5.58578644,2 L2,2 L2,5 L1,5 L1,1 L6,1 Z'
                    }`"
                    stroke-width="1"
                    fill="#8a8e99"
                  ></path>
                </svg>
                <span class="tree-node-label">{{ node.label }}</span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧配置表单 -->
      <div class="config-form">
        <div v-if="!selectedProvince" class="empty-state">
          <el-empty description="请选择后进行配置" />
        </div>
        <div v-else-if="loading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else class="form-container">
          <div class="form-header">
            <h3>{{ selectedProvince.name }} - 变更流转配置</h3>
          </div>

          <el-form
            ref="configFormRef"
            :model="configForm"
            :rules="configRules"
            label-width="140px"
            label-position="left"
          >
            <el-form-item label="项目概算金额" prop="projectBudgetAmount">
              <el-input-number
                v-model="configForm.projectBudgetAmount"
                :min="0"
                :precision="0"
                controls-position="right"
                style="width: 200px;"
                placeholder="请输入金额"
              />
              <span class="unit">万元</span>
              <el-tooltip
                placement="top"
                effect="dark"
                popper-class="approval-tooltip"
              >
                <template #content>
                  <div class="tooltip-content">
                    <div class="tooltip-title">审批权限说明：</div>
                    <div class="tooltip-rule">金额 ≥ {{ configForm.projectBudgetAmount || 'X' }} 万元：由省分公司审批</div>
                    <div class="tooltip-rule">金额 ＜ {{ configForm.projectBudgetAmount || 'X' }} 万元：由地市分公司审批</div>
                    <div class="tooltip-special">特殊规则：若申请人为省分用户，无论金额大小，均由省分公司审批</div>
                  </div>
                </template>
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :disabled="!selectedProvince"
          :loading="saving"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, getCurrentInstance, onMounted, nextTick } from 'vue';
import { Search, InfoFilled } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useUserStoreHook } from '@/store/modules/user';
import {
  getAllProvinces,
  getProvinceFlowConfig,
  saveProvinceFlowConfig,
  type ProvinceInfo,
  type ProvinceFlowConfig
} from '../api/index';

const { $message } = getCurrentInstance()!.appContext.config.globalProperties;

// 获取用户信息
const userStore = useUserStoreHook();
const userInfo = computed(() => userStore.getUserSessionInfo);

// 权限判断
const isSuperAdmin = computed(() => {
  return userInfo.value?.maxRoleLevel === 0;
});

const isProvinceAdmin = computed(() => {
  return userInfo.value?.maxRoleLevel === 1;
});

// 当前用户所属省份（模拟数据，实际应从用户信息中获取）
const currentUserProvince = computed(() => {
  // 这里应该从用户信息中获取用户所属的省份
  // 暂时使用模拟数据
  if (isSuperAdmin.value) {
    return null; // 超级管理员可以看到所有省份
  }
  // 省份管理员只能看到自己的省份，这里需要根据实际的用户信息来判断
  return userInfo.value?.zoneId || 'shanghai'; // 假设用户属于上海分公司
});



// 组件属性
const props = defineProps<{
  modelValue: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'save': [data: any];
}>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const searchKeyword = ref('');
const selectedProvince = ref<ProvinceInfo | null>(null);
const configFormRef = ref<FormInstance>();
const saving = ref(false);
const loading = ref(false);
const allProvinceList = ref<ProvinceInfo[]>([]);
const provinceTreeRef = ref();

// 配置表单数据
const configForm = reactive({
  projectBudgetAmount: 3000
});

// 表单验证规则
const configRules: FormRules = {
  projectBudgetAmount: [
    { required: true, message: '请输入项目概算金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ]
};

// 树组件配置
const treeProps = {
  label: 'name',
  children: 'children'
};

// 构建省份树数据 - 添加固定的"全部"节点
const provinceTreeData = computed(() => {
  let filteredProvinces = [];

  if (isSuperAdmin.value) {
    // 超级管理员可以看到所有省份，不做任何过滤
    filteredProvinces = [...allProvinceList.value];
  } else if (isProvinceAdmin.value) {
    // 省份管理员只能看到自己的省份
    filteredProvinces = allProvinceList.value.filter(province =>
      province.code === currentUserProvince.value
    );
  }

  // 根据搜索关键词过滤省份
  if (searchKeyword.value) {
    filteredProvinces = filteredProvinces.filter(province =>
      province.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 返回树结构：固定的"全部"根节点 + 省份子节点
  return [
    {
      code: 'all',
      name: '全部',
      hasConfig: true,
      isLeaf: false,
      children: filteredProvinces.map(province => ({
        ...province,
        isLeaf: true
      }))
    }
  ];
});

// 树节点点击处理 - 支持"全部"节点和省份节点
const handleTreeNodeClick = (nodeData: any) => {
  console.log("选中节点:", nodeData);

  if (nodeData && nodeData.code !== 'all') {
    // 选择具体省份节点
    selectedProvince.value = nodeData;
    loadProvinceConfig(nodeData.code);
  } else if (nodeData && nodeData.code === 'all') {
    // 选择"全部"节点，清空右侧表单
    selectedProvince.value = null;
    configFormRef.value?.resetFields();
    // 重置表单为默认值
    Object.assign(configForm, {
      projectBudgetAmount: 3000
    });
  } else {
    // 其他情况，清空选择
    selectedProvince.value = null;
    configFormRef.value?.resetFields();
  }
};

// 加载省份配置
const loadProvinceConfig = async (provinceCode: string) => {
  try {
    loading.value = true;
    const config = await getProvinceFlowConfig(provinceCode);

    if (config) {
      Object.assign(configForm, {
        projectBudgetAmount: config.projectBudgetAmount || 3000
      });
    } else {
      // 默认配置
      Object.assign(configForm, {
        projectBudgetAmount: 3000
      });
    }
  } catch (error) {
    console.error('加载省份配置失败:', error);
    $message.error('加载配置失败');
  } finally {
    loading.value = false;
  }
};

// 保存配置
const handleSave = async () => {
  if (!configFormRef.value || !selectedProvince.value) return;

  try {
    await configFormRef.value.validate();

    saving.value = true;

    const configData: ProvinceFlowConfig = {
      provinceCode: selectedProvince.value.code,
      provinceName: selectedProvince.value.name,
      projectBudgetAmount: configForm.projectBudgetAmount
    };

    const result = await saveProvinceFlowConfig(configData);

    if (result.success) {
      emit('save', configData);
      // $message.success(result.message);

      // 更新省份配置状态
      const province = allProvinceList.value.find(p => p.code === selectedProvince.value!.code);
      if (province) {
        province.hasConfig = true;
      }
    } else {
      $message.error(result.message || '保存失败');
    }

  } catch (error) {
    console.error('保存配置失败:', error);
    $message.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  selectedProvince.value = null;
  configFormRef.value?.resetFields();
  // 清空树的选中状态，下次打开时会重新设置为"全部"
  if (provinceTreeRef.value) {
    provinceTreeRef.value.setCurrentKey(null);
  }
};



// 加载省份列表
const loadProvinceList = async () => {
  try {
    const provinces = await getAllProvinces();
    allProvinceList.value = provinces;
  } catch (error) {
    console.error('加载省份列表失败:', error);
    $message.error('加载省份列表失败');
  }
};

// 监听弹窗打开
watch(visible, async (newVal) => {
  if (newVal) {
    // 检查权限
    if (!isSuperAdmin.value && !isProvinceAdmin.value) {
      $message.error('您没有权限访问此功能');
      visible.value = false;
      return;
    }

    // 加载省份列表
    await loadProvinceList();

    // 默认选中"全部"节点
    if (provinceTreeRef.value) {
      // 使用 nextTick 确保树组件已经渲染完成
      await nextTick();
      provinceTreeRef.value.setCurrentKey('all');
      // 触发"全部"节点的点击事件，清空右侧表单
      handleTreeNodeClick({ code: 'all', name: '全部' });
    }
  }
});

// 组件挂载时加载数据
onMounted(() => {
  loadProvinceList();
});
</script>

<style lang="scss" scoped>
.config-container {
  display: flex;
  height: 600px;
  gap: 20px;

  .province-tree {
    width: 300px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);

    .tree-header {
      padding: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .header-title {
        font-weight: 500;
        color: #ffffff;
        font-size: 16px;
        display: block;
        margin-bottom: 8px;
      }
    }

    .tree-content {
      height: calc(100% - 95px);
      overflow-y: auto;
      padding: 8px;

      :deep(.el-tree) {
        background: transparent;

        .el-tree-node {
          .el-tree-node__content {
            color: rgba(255, 255, 255, 0.85);
            background: transparent;
            padding: 8px 4px;
            border-radius: 4px;

            &:hover {
              background-color: rgba(255, 255, 255, 0.08);
            }

            .tree-node-content {
              display: flex;
              align-items: center;
              flex: 1;
              min-width: 0;
              gap: 4px;

              .tree-node-label {
                flex: 1;
                font-size: 13px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                min-width: 0;
              }
            }
          }

          &.is-current > .el-tree-node__content {
            background-color: rgba(64, 158, 255, 0.2);
            color: var(--el-color-primary);

            .tree-node-content {
              .tree-node-label {
                color: var(--el-color-primary);
                font-weight: 500;
              }
            }
          }

          .el-tree-node__expand-icon {
            color: rgba(255, 255, 255, 0.65);

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
    }
  }

  .config-form {
    flex: 1;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);

    .empty-state,
    .loading-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
    }

    .form-container {
      padding: 20px;
      height: 100%;
      overflow-y: auto;

      .form-header {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        h3 {
          margin: 0;
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .unit {
        margin-left: 8px;
        color: rgba(255, 255, 255, 0.65);
        font-size: 14px;
      }

      .info-icon {
        margin-left: 8px;
        color: var(--el-color-primary);
        cursor: help;
        font-size: 16px;
      }
    }
  }
}

// 表单样式覆盖
:deep(.el-form-item__label) {
  color: #ffffff !important;
}

:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);

  .el-input__inner {
    background: transparent;
    color: #ffffff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .el-input__inner {
      background: transparent;
      color: #ffffff;
    }
  }
}

:deep(.el-select) {
  .el-select__wrapper {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    .el-select__selected-item {
      color: #ffffff;
    }

    .el-select__caret {
      color: #ffffff;
    }
  }
}

:deep(.el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;

  &::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

:deep(.el-tag) {
  &.el-tag--success {
    background: rgba(103, 194, 58, 0.2);
    border-color: rgba(103, 194, 58, 0.4);
    color: #67c23a;
  }

  &.el-tag--info {
    background: rgba(144, 147, 153, 0.2);
    border-color: rgba(144, 147, 153, 0.4);
    color: #909399;
  }
}

// 自定义tooltip样式
:deep(.approval-tooltip) {
  .tooltip-content {
    .tooltip-title {
      font-weight: 600;
      margin-bottom: 8px;
      color: #ffffff;
      font-size: 14px;
    }

    .tooltip-rule {
      margin-bottom: 4px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 13px;
      line-height: 1.4;
    }

    .tooltip-special {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      color: #f56c6c;
      font-size: 13px;
      line-height: 1.4;
      font-weight: 500;
    }
  }
}
</style>
