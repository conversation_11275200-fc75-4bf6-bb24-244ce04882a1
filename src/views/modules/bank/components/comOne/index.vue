<template>
	<div class="container-archive">
		<div class="catalogModule" :style="`width: ${state.isRightIcon ? 280 : 520}px;`">
			<comOne @catalog-module="CatalogModuleClickFn" 
				:parentData="state.parentData"></comOne>
			<div class="rightIcon" @click="isRightIconFn">
				<img v-show="state.isRightIcon" src="../../../../assets/img/bank/BK_1.png" alt="">
				<img v-show="!state.isRightIcon" src="../../../../assets/img/bank/BK_4.png" alt="">
			</div>
		</div>
		<div class="dataModule" :style="`width: ${state.isRightIcon ? 1280 : 1080}px;`">
			<comTwo v-if="state.tableWidth" :tableData="state.tableData" @size-Change="sizeChangeFn" @current-Change="currentChangeFn" :pageObj="state.pageObj" :pathData="state.pathData"
				@clear-PathData="clearPathDataFn" v-show="state.isCompanyListClick" @search-change="queryDataDirectoryFn"></comTwo>
			<div class="centerDiv" v-show="!state.isCompanyListClick">请选择目录</div>
		</div>
	</div>
  </template>
  
  <script lang="ts" setup>
  import { reactive, onMounted,onUnmounted, onBeforeMount, ref, defineEmits, watch,nextTick } from 'vue'
  import CompanyList from './company/CompanyList.vue'
  import comOne from './comOne.vue'
  import comTwo from './comTwo.vue'
import { queryCompanyFilesProject, queryDataDirectory,queryPageDataDirectory } from '@/script/api/common/commomApi'
import { useRoute  } from 'vue-router';
import { ElMessage } from 'element-plus'
const route = useRoute();
const state = reactive({
	isRightIcon: true,
	parentData: [],
	isCompanyListClick: false,
	tableWidth: true,
	pathData: [],
	tableData: [],
	collectMapList: [],
	projectId: '',
	shortName: '',
	isType: 0,
	pageObj: {
		pageSize: 10,
		pageNo: 1,
		total: 1000,
	paramsFn:''
  }
})
onMounted(async () => {
	state.projectId = route.query.projectId
	state.shortName = route.query.shortName
	queryDataDirectoryFn(null, 'first')
})
const sizeChangeFn = (e: any) => {
	state.pageObj.pageSize = e
	queryPageDataDirectoryFn(state.paramsFn)
}
const currentChangeFn = (e: any) => {
	state.pageObj.pageNo = e
	queryPageDataDirectoryFn(state.paramsFn)
}
const queryPageDataDirectoryFn = async (params:any) => {
	params.pageNo = state.pageObj.pageNo
	params.pageSize = state.pageObj.pageSize
	const resTable = await queryPageDataDirectory(params)
	state.tableData = resTable.data.list
	state.pageObj.total = resTable.data.total
}
// 请求列表数据
const queryDataDirectoryFn = async (datas: any = null, num: any) => {
	let params = {"projectId": state.projectId,}

	if (datas !== null) { params[datas.label] = datas.val }
	state.paramsFn = params
	const res = await queryDataDirectory(params)
	
	if (res.status === 0) {
	 state.isType = res.data.type
		if (num === 'first') {
			state.parentData = res.data.directoryVOList
		}
		queryPageDataDirectoryFn(params)
		// state.tableData = res.data.dataFileVOList
	} else {
		ElMessage.error('请求失败')
	}
}
// 左二点击事件
const CatalogModuleClickFn = (e: any, node: any) => {
	state.isCompanyListClick = true

	if (node.level === 1) {
		console.log(e,'eeeeeeeeee');
		
		queryDataDirectoryFn({ label: 'firstDirectoryId', val: e.firstDirectoryId })
		state.pathData = [{label:e.firstDirectoryName,idName:'firstDirectoryId',id:e.firstDirectoryId}]
	} else {
		state.pathData = [
			{
				label: node.parent.data.firstDirectoryName,
				idName: 'firstDirectoryId',
				id: node.parent.data.firstDirectoryId
			},
			{
				label: e.secondDirectoryName,
				idName: 'secondDirectoryId',
				id: e.secondDirectoryId,
				secondDescribe:e.secondDescribe
			}]
		queryDataDirectoryFn({ label: 'secondDirectoryId', val: e.secondDirectoryId })
	}

}
// 面包屑点击
const clearPathDataFn = (arr: any) => {
	state.pathData = arr
	if (arr.length > 0) {
		queryDataDirectoryFn({ label: arr[arr.length - 1].idName, val: arr[arr.length - 1].id})
	} else {
		queryDataDirectoryFn(null)
	}
	
}
  // 左二模块放大缩小
  const isRightIconFn = () => {
	  state.tableWidth = false;
	  state.isRightIcon = !state.isRightIcon
	  setTimeout(() => {
		   state.tableWidth = true;
	  })
	  
  }
  
  </script>
  
  <style lang="scss" scoped>
  @mixin border-same {
	  border-radius: 4px 4px 4px 4px;
	  border: 1px solid;
	  border-image: linear-gradient(
			  180deg,
			  rgba(46.905815452337265, 67.59302258491516, 109.28571537137032, 1),
			  rgba(31.5929202362895, 49.304467141628265, 85.0000025331974, 1)
		  )
		  1 1;
  }
  @mixin flexCenter{
	  display: flex;
	  justify-content: space-between;
	  align-items: center;
  }
//   :deep(.el-scrollbar__thumb) {
// 	  background-color: #0C417F;
// 	  opacity: 1;
//   }
  .container-archive{
	  width: 100%;
	  height: 100%;
	  display: flex;
	  .centerDiv{
			  line-height: 1080px;
			  font-size: 20px;
			  text-align: center;
			  color: black;
		  }
	  .catalogModule{
		  height: 1080px;
		  position: relative;
		  padding-top: 20px;
		  padding-left: 20px;
		  padding-bottom: 50px;
		  padding-right: 20px;
		  border-right:1px solid;
		  
		  .rightIcon{
			  cursor: pointer;
			  width: 36px;
			  position: absolute;
			  right: -18px;
			  top:40%;
			  img{
				  width: 100%;
			  }
		  }
		  .bomBtn{
			  width: 100%;
			  padding-right: 18px;
			  position: absolute;
			  bottom: 0;
			  right: 0;
			  font-size: 20px;
			  @include flexCenter;
			  .bomBtn_l{
				  cursor: pointer;
				  @include flexCenter;
				  img{
					  width: 38px;
				  }
			  }
			  .bomBtn_r{
				  cursor: pointer;
			  }
		  }
	  }
	  .dataModule{
		  margin-top: 15px;
		  margin-left: 26px;
		  // flex-grow: 1;
		  
		  height: 1080px;
		  position: relative;
		  
	  }
  }
  </style>