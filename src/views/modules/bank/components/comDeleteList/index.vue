<template>
	<div class="table-container">
		<div class="leftTop">
			<img src="../../../../assets/img/bank/BK_2.png" alt="">
			{{ route.query.shortName }}
		</div>
		<div class="table-srarch">
			<div class="srarch_title">
				<!-- <div class="title_t">资料下载权限管理</div> -->
			</div>


		</div>
		<div class="search_bom">
			<el-input class="search_inp" size="large"  v-model="state.filterText"
				style="width: 300px" placeholder="请输入资料名称" />
				<el-select class="search_inp" size="large"  v-model="state.selectOperateType"
				style="width: 300px" placeholder="请选择操作类型" >
				<el-option label="上传" value="1">上传</el-option>
				<el-option label="下载" value="2">下载</el-option>
				<el-option label="删除" value="3">删除</el-option>
			</el-select>
			<el-button size="large" type="primary" @click="queryList(false)">查询</el-button>
			<el-button size="large" @click="queryList(true)">重置</el-button>
		</div>

		<div class="table_Data">
			<el-table class="transparent-table" :data="state.tableData" style="width: 100%">
				<el-table-column prop="fileName" label="资料名称" width="220" />
				<el-table-column prop="fileDirectoryPath" show-overflow-tooltip label="位置" width="280" />

				<el-table-column prop="createTime" sortable width="210" label="删除时间" />
				<el-table-column prop="fileSize" label="文件大小" width="120">
					<template #default="scope">
						{{ scope.row.fileSize + 'MB' }}
					</template>
				</el-table-column>
				<el-table-column prop="uploadUser" label="操作人" width="120" />
				<el-table-column prop="operateType" label="操作类型" width="120">
					<template #default="scope">
						{{  ['上传','下载','删除'][scope.row.operateType- 1] }}
					</template>
				</el-table-column>
			</el-table>
			<el-pagination :page-size="state.pageObj.pageSize" @size-change="sizeChangeFn"
				:current-page="state.pageObj.pageNo" @current-change="currentChangeFn" background
				layout="total, sizes, prev, pager, next, jumper" :total="state.pageObj.total" />
		</div>

	</div>


</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import { queryPageDataDirectoryDelete } from '@/script/api/common/commomApi'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute();
const filterText = ref('')
const selectOperateType = ref('')

const ruleFormRef = ref<FormInstance>()
const state = reactive({
	selectOperateType: '',
	filterText:'',
	multipleSelection: [],
	tableData: [

	],
	pageObj: {
		pageSize: 10,
		pageNo: 1,
		total: 0,
	},
	form: {
		name: '',
		jobNumber: '',
		telephone: '',
		orgName: '',
	},
	dialogFormVisible: false,
	formLabelWidth: '140px',
	rules: {
		name: [
			{ required: true, message: '请输入姓名', trigger: 'blur' },
			// { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
		],
		jobNumber: [
			{ required: true, message: '请输入工号', trigger: 'blur' },
		],
		telephone: [
			{ required: true, message: '请输入手机号', trigger: 'blur' },
		],
	}
})
// 默认查询
onMounted(() => {
	queryList(false)
})

// 列表查询
const queryList = async (e: any) => {
	
	const params = {
		fileName:state.filterText,
		operateType:state.selectOperateType,  
		projectId: route.query.projectId,
		//   operateType: 3,
		pageNo: state.pageObj.pageNo,
		pageSize: state.pageObj.pageSize,
	}
	if (e) {
		state.filterText = ''
		state.selectOperateType = ''
		params.fileName = ''
		params.operateType = ''
	}
	const res = await queryPageDataDirectoryDelete(params)
	state.tableData = res.data.list
	state.pageObj.total = res.data.total
	console.log(res, 'listlistlistlist');

}
// 新增表单提交
const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate((valid, fields) => {
		if (valid) {
			addDataFilePermissionsFn()

		} else {
			console.log('error submit!', fields)
		}
	})
}
// 重置按钮
const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
	state.dialogFormVisible = false
}
// 新增接口
const addDataFilePermissionsFn = async () => {
	const params = {
		projectId: route.query.projectId,
		type: 1,
		...state.form
	}
	const res = await addDataFilePermissions(params)
	if (res.status === 0) {
		ElMessage({
			message: '新增成功',
			type: 'success',
		})
		queryList(false)
		resetForm(ruleFormRef.value)
	}
	console.log(res, 'resresresresresresres');
}
// 点击关闭按钮
const handleClose = () => {
	resetForm(ruleFormRef.value)

}
// 分页器条数
const sizeChangeFn = (e: any) => {
	state.pageObj.pageSize = e
	queryList(false)
}
// 分页器页码
const currentChangeFn = (e: any) => {
	state.pageObj.pageNo = e
	queryList(false)
}
// 删除
const handleClick = (e: any) => {
	ElMessageBox.confirm(
		'确定要删除吗?',
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(async () => {
			const res = await deleteDataFilePermissions({ id: e.id })
			if (res.status === 0) {
				ElMessage({
					message: '删除成功',
					type: 'success',
				})
				queryList(false)
			}
		})
		.catch(() => {
		})

}
</script>
<style lang="scss" scoped>
@mixin flexCenter {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

:deep(.el-breadcrumb__inner) {
	--el-text-color-regular: #D3D6DD !important;
	--el-text-color-primary: #D3D6DD !important;
	font-weight: normal !important;
	font-size: 20px !important;
}

:deep(.el-breadcrumb__separator) {
	color: #D3D6DD !important;
}

// :deep( .search_inp .el-input__wrapper){
// 		border: 1px solid #4aa8f5 !important;
// 			font-size: 20px;
// 			box-shadow: none !important; /* 去掉阴影 */
// 			border-radius:8px;
// 			background-color: #182137!important;
// 		}
:deep(.el-table) {
	--el-table-border-color: #fff;
	--el-table-row-hover-bg-color: #fff;
	--el-table-header-bg-color: #fff;
	--el-table-tr-bg-color: #fff;
	// background-color:#182137;
	--el-table-header-text-color: #000;
	font-size: 20px;
	// color: #D3D6DD;

}

:deep(.el-checkbox__inner) {
	background-color: transparent;
	border-radius: 0px;
	border-color: gray;
}

:deep(.el-pagination) {
	position: absolute;
	bottom: 50px;
	right: 40px;
	--el-pagination-font-size: 20px;
	// --el-pagination-button-color: #629DE3;
	// --el-pagination-button-bg-color: #182137
}

.search_inp {
	margin-right: 20px;
}

.table-container {
	padding-top: 20px;
	padding-left: 20px;

	.leftTop {
		color: #000;

		img {
			display: block;
			margin-right: 15px;
		}

		font-size: 20px;
		display: flex;
		align-items: center;
	}

	.table-srarch {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 30px;
		font-size: 24px;
		font-weight: bold;
		color: black;

		.title_b {
			display: flex;
			align-items: center;
			margin-top: 20px;
			font-size: 16px;
			font-weight: normal;
			color: rgb(46, 45, 45);

		}

	}

	.search_bom {
		margin-top: 40px;
	}

	.table_Data {
		width: 100%;
		margin-top: 40px;
	}

	.bomBtn {
		width: 100%;
		padding-right: 18px;
		position: absolute;
		bottom: 0;
		right: 0;
		font-size: 20px;
		@include flexCenter;

		.bomBtn_l {
			// cursor: pointer;
			@include flexCenter;

			img {
				width: 38px;
			}
		}
	}
}

.fs20 {
	font-size: 20px;
}

.formDia {
	margin-right: 30px;
}
</style>