<template>
  <div class="table-container">
    <div class="table-top">
    </div>
    <div class="table-srarch">
      <div class="srarch_title">
        <div class="title_t">{{ props.pathData.length > 0 ? props.pathData[props.pathData.length - 1].label : '档案上传' }}
        </div>
        <div class="title_b" v-if="props.pathData.length === 2">
          <img src="../../../../../assets/img/bank/BK_3.png" alt="">
          {{ props.pathData[props.pathData.length - 1].secondDescribe }}
        </div>
      </div>


    </div>
    <div class="search_bom">
      <el-input class="search_inp" size="large" :suffix-icon="Search" @change="emitSearchFn" v-model="filterText"
        style="width: 300px" placeholder="搜索资料名称" />
    </div>

    <div class="table_Data">
      <el-table class="transparent-table" height="614" :data="props.tableData" style="width: 100%">
        <el-table-column prop="fileName" fixed label="资料名称" width="220" />
        <el-table-column prop="fileDirectoryPath" show-overflow-tooltip label="位置" width="280" />
        <el-table-column prop="uploadUser" label="删除人" width="120" />
        <el-table-column prop="modifyTime" fixed="right" sortable width="210" label="删除时间" />
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="scope">
            {{ scope.row.fileSize + 'MB' }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :page-size="props.pageObj.pageSize" @size-change="sizeChangeFn"
        :current-page="props.pageObj.pageNo" @current-change="currentChangeFn" background
        layout="total, sizes, prev, pager, next, jumper" :total="props.pageObj.total" />
    </div>

  </div>


</template>

<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, ref, defineEmits, watch, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { upload, deleteFile } from '@/script/api/common/commomApi'
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import type { UploadProps } from 'element-plus'
const route = useRoute();
const filterText = ref('')
const props = defineProps({
  pathData: Array,
  tableData: Array,
  pageObj: Object
});
const state = reactive({
  nowNum: 0,
  fileList: [],
  fileList1: [],
  isFold: false,
  fullscreenLoading: false,
  firstUpload:true,
})
const emit = defineEmits(['search-change', 'size-Change', 'current-Change',])
const sizeChangeFn = (e: any) => {
  emit('size-Change', e)
}
const currentChangeFn = (e: any) => {
  emit('current-Change', e)
}

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning(
    '批量上传文件最大数量限制50个文件！'
  )
}

const handleClick = (e: any) => {
  ElMessageBox.confirm(
    '确定要删除吗?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // state.fullscreenLoading = true;
      const res = await deleteFile({ id: e.id })
      if (res.status === 0) {
        // state.fullscreenLoading = false;

        ElMessage({
          message: res.data,
          type: 'success',
        })
        sizeChangeFn(props.pageObj.pageSize)
      }
    })
    .catch(() => {
    })

}


const emitSearchFn = (e: any) => {
  console.log(e, 'eeeeeeeeeee');

  emit('search-change', { label: 'fileName', val: e })

}
const uploadFlie = async (fileList:any) => {
  const formData = new FormData()
  formData.append('file', fileList[state.nowNum].raw)
  formData.append('projectId', route.query.projectId + "")
  formData.append('firstDirId', props.pathData[0].id + "")
  formData.append('secondDirId', props.pathData[1].id + "")
  // state.fullscreenLoading = true;
  const res = await upload(formData)
  if (res.status === 0) {
    if (state.nowNum === fileList.length - 1) {
      state.firstUpload = true
      state.nowNum = 0
      ElMessage({
      message: res.data,
      type: 'success',
      })
      sizeChangeFn(props.pageObj.pageSize)
    } else {
      state.nowNum += 1
      uploadFlie(fileList)
    }
    // state.fullscreenLoading = false;
   
  }
}
const handleChange =  (file: any) => {

  if (file.size / 1024 / 1024 > 200) {
    ElMessage({
      message: "文件不能大于200MB!",
      type: 'error',
    })
    return false
  }
  if (state.firstUpload) {
    setTimeout(() => {
      state.fileList1 = state.fileList
      uploadFlie(state.fileList)
    })
    state.firstUpload = false
  }
 
 
 


}

</script>
<style lang="scss" scoped>
@mixin flexCenter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-breadcrumb__inner) {
  --el-text-color-regular: #D3D6DD !important;
  --el-text-color-primary: #D3D6DD !important;
  font-weight: normal !important;
  font-size: 20px !important;
}

:deep(.el-breadcrumb__separator) {
  color: #D3D6DD !important;
}

// :deep( .search_inp .el-input__wrapper){
// 		border: 1px solid #4aa8f5 !important;
// 			font-size: 20px;
// 			box-shadow: none !important; /* 去掉阴影 */
// 			border-radius:8px;
// 			background-color: #182137!important;
// 		}
:deep(.el-table) {
  --el-table-border-color: #fff;
  --el-table-row-hover-bg-color: #fff;
  --el-table-header-bg-color: #fff;
  --el-table-tr-bg-color: #fff;
  // background-color:#182137;
  --el-table-header-text-color: #000;
  font-size: 16px;
  // color: #D3D6DD;
}

:deep(.el-checkbox__inner) {
  background-color: transparent;
  border-radius: 0px;
  border-color: gray;
}

:deep(.el-pagination) {
  position: absolute;
  bottom: 50px;
  right: 40px;
  --el-pagination-font-size: 20px;
  // --el-pagination-button-color: #629DE3;
  // --el-pagination-button-bg-color: #182137
}

.search_inp {
  margin-right: 20px;
}

.table-container {
  padding-right: 40px;

  .table-top {
    margin-top: 20px;
  }

  .table-srarch {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 24px;
    font-weight: bold;
    color: black;

    .title_b {
      display: flex;
      align-items: center;
      margin-top: 20px;
      font-size: 16px;
      font-weight: normal;
      color: rgb(46, 45, 45);

    }

  }

  .search_bom {
    margin-top: 40px;
    display: flex;
    align-items: center;

    .upload-demo {
      display: flex;
      align-items: center;
    }
  }

  .table_Data {
    width: 100%;
    margin-top: 40px;
  }

  .bomBtn {
    width: 100%;
    padding-right: 18px;
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 20px;
    @include flexCenter;

    .bomBtn_l {
      // cursor: pointer;
      @include flexCenter;

      img {
        width: 38px;
      }
    }
  }


}


.boxcontain {
  position: fixed;
  bottom: 22px;
  right: 64px;
  width: 712px;
  z-index: 99999;
  background: #ffffff;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
}

.topContain {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #eeeeee;
  padding-left: 32px;
  padding-right: 32px;

  span {
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .logoImage {
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }

  .closeImage {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
}

.flex_row {
  display: flex;
  align-items: center;
}

.middleContain {
  display: flex;
  flex-direction: column;
}



.item_contain {
  position: relative;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 32px;
  padding-right: 32px;

  .item_left_contain {
    display: flex;
    align-items: center;
    z-index: 100;

    img {
      width: 24px;
      height: 32px;
      margin-right: 16px;
      object-fit: contain;
    }

    .filediv {
      height: 20px;
      line-height: 20px;
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      overflow: hidden;
      width: 300px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .spandiv {
      margin-top: 4px;
      height: 17px;
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 17px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .item_right_contain {
    z-index: 100;
    
    color: #000;
    div{
      display: flex;
    justify-content: space-between;
    align-items: center;
    span{
      margin-right: 10px;
    }
    }
  }
}

.bottomContain {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;

  span {
    font-size: 20px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    cursor: pointer;
  }
}
</style>