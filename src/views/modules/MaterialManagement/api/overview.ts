import { http } from '@/utils/http';

// 服务名配置 - 方便后续更换服务名
const SERVICE_NAME = 'api';

// API返回的原始数据结构
export interface MaterialOverviewApiResponse {
  total: number;
  provinceCount: number;
  jcCount: number;
}

// 设备数量统计API返回的数据结构
export interface QuantityApiResponse {
  sub_quantity: number;   // 缺口数量
  plan_quantity: number;  // 规划设备数量
  buy_quantity: number;   // 采购设备数量
}

// 总览数据接口 - 组件内部使用的数据结构
export interface OverviewData {
  totalMaterials: number;    // 对应API返回的 total
  completedMaterials: number; // 对应API返回的 provinceCount
  basicProjects: number;     // 对应API返回的 jcCount
  // 新增的设备数量统计字段
  planQuantity: number;      // 规划设备数量
  buyQuantity: number;       // 采购设备数量
  subQuantity: number;       // 缺口数量
}

// 获取总览数据 - 使用真实接口
export const getMaterialOverviewData = () => {
  return http.get<any, RestResult<MaterialOverviewApiResponse>>(`/${SERVICE_NAME}/materials/materialOverview`);
};

// 获取设备数量统计数据 - 新增接口
export const getQuantityData = () => {
  return http.get<any, RestResult<QuantityApiResponse>>(`/${SERVICE_NAME}/materials/queryQuantity`);
};

// 数据转换函数：将API返回的数据转换为组件需要的格式
export const transformOverviewData = (apiData: MaterialOverviewApiResponse | null, quantityData: QuantityApiResponse | null = null): OverviewData => {
  if (!apiData) {
    // 当接口报错或返回为空时，默认所有值都是0
    return {
      totalMaterials: 0,
      completedMaterials: 0,
      basicProjects: 0,
      planQuantity: quantityData?.plan_quantity || 0,
      buyQuantity: quantityData?.buy_quantity || 0,
      subQuantity: quantityData?.sub_quantity || 0
    };
  }

  return {
    totalMaterials: apiData.total || 0,
    completedMaterials: apiData.provinceCount || 0,
    basicProjects: apiData.jcCount || 0,
    planQuantity: quantityData?.plan_quantity || 0,
    buyQuantity: quantityData?.buy_quantity || 0,
    subQuantity: quantityData?.sub_quantity || 0
  };
};
