<template>
  <div class="table-container">
    <div class="table-top">
    </div>
    <div class="table-srarch">
      <div class="srarch_title">
       
      </div>
     

    </div>
    <div class="search_bom">
      
      <el-button type="primary" :disabled="!props.isCompanyListClick" @click="state.dialogFormVisible = true" size="large">添加人员</el-button>
      </div>

    <div class="table_Data">
      <el-table class="transparent-table" height="614"  :data="props.tableData" style="width: 100%">
        <el-table-column prop="name" fixed label="姓名" width="320" />
        <el-table-column prop="jobNumber" label="联通工号(7位数字)" width="220" />
        <el-table-column prop="telephone" label="手机号" width="220" />
        <el-table-column prop="orgName" label="部门" width="320" />
        <el-table-column fixed="right" label="操作" min-width="180">
          <template #default="scope">
            <el-button class="fs20" link type="primary" @click="handleClick(scope.row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :page-size="props.pageObj.pageSize"
      @size-change="sizeChangeFn"
      :current-page="props.pageObj.pageNo"
      @current-change="currentChangeFn"
       background layout="total, sizes, prev, pager, next, jumper" :total="props.pageObj.total" />
    </div>
    <el-dialog :close-on-click-modal="false" :before-close="handleClose" v-model="state.dialogFormVisible" title="添加人员" width="500">

<el-form ref="ruleFormRef" :rules="state.rules" class="formDia" size="large" :model="state.form">
  <el-form-item label="姓名" prop="name" :label-width="state.formLabelWidth">
    <el-input v-model="state.form.name" autocomplete="off" />
  </el-form-item>
  <el-form-item label="联通工号(7位数字)" prop="jobNumber" :label-width="state.formLabelWidth">
    <el-input v-model="state.form.jobNumber" autocomplete="off" />
  </el-form-item>
  <el-form-item label="手机号" prop="telephone" :label-width="state.formLabelWidth">
    <el-input v-model="state.form.telephone" autocomplete="off" />
  </el-form-item>
  <el-form-item label="部门" prop="orgName" :label-width="state.formLabelWidth">
    <el-input v-model="state.form.orgName" autocomplete="off" />
  </el-form-item>

</el-form>
<template #footer>
  <div class="dialog-footer">
    <el-button @click="resetForm(ruleFormRef)">取消</el-button>
    <el-button type="primary" @click="submitForm(ruleFormRef)">
      确定添加
    </el-button>
  </div>
</template>
</el-dialog>
			
  </div>


</template>

<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, ref, defineEmits, watch, nextTick } from 'vue'
import {  Search } from '@element-plus/icons-vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { upload ,deleteFile, addDataFilePermissions, deleteDataFilePermissions} from '@/script/api/common/commomApi'
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox, ElLoading,FormInstance } from 'element-plus'
const route = useRoute();
const filterText = ref('')
const ruleFormRef = ref<FormInstance>()
const props = defineProps({
  tableData: Array,
  pageObj: Object,
  isCompanyListClick:Boolean
});
const state = reactive({
  fileList: [],
  fullscreenLoading: false,
  form: {
    name: '',
    jobNumber: '',
    telephone: '',
    orgName: '',
  },
  dialogFormVisible: false,
  formLabelWidth: '140px',
  rules: {
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      // { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
    ],
    jobNumber: [
      { required: true, message: '请输入工号', trigger: 'blur' },
    ],
    telephone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
    ],
  }
})
const emit = defineEmits(['search-change', 'size-Change', 'current-Change',])
const sizeChangeFn = (e: any) => {
  emit('size-Change',e)
}
const currentChangeFn = (e: any) => {
  emit('current-Change',e)
}
const handleClick =  (e: any) => {
  ElMessageBox.confirm(
    '确定要删除吗?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      state.fullscreenLoading = true;
        const res = await deleteDataFilePermissions({id:e.id})
      if (res.status === 0) {
      state.fullscreenLoading = false;

      ElMessage({
        message: '删除成功',
        type: 'success',
      })
      sizeChangeFn(props.pageObj.pageSize)
    }
    })
    .catch(() => {
    })
 
}
// 新增表单提交
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      addDataFilePermissionsFn()
      
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 重置按钮
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  state.dialogFormVisible = false
}
// 新增接口
const addDataFilePermissionsFn = async () => {
  const params = {
    projectId: sessionStorage.getItem('bank1projectId'),
    type: 2,
    ...state.form
  }
  const res = await addDataFilePermissions(params)
  if (res.status === 0) {
    ElMessage({
    message: '新增成功',
    type: 'success',
    })
    emit('search-change',sessionStorage.getItem('bank1projectId'))
    resetForm(ruleFormRef.value)
  }
  console.log(res,'resresresresresresres');
}
// 点击关闭按钮
const handleClose = () => {
  resetForm(ruleFormRef.value)
  
}
</script>
<style lang="scss" scoped>
@mixin flexCenter{
	display: flex;
	justify-content: space-between;
	align-items: center;
}
:deep(.el-breadcrumb__inner) {
  --el-text-color-regular: #D3D6DD !important;
  --el-text-color-primary: #D3D6DD !important;
  font-weight: normal !important;
  font-size: 20px !important;
}

:deep(.el-breadcrumb__separator) {
  color: #D3D6DD !important;
}
// :deep( .search_inp .el-input__wrapper){
// 		border: 1px solid #4aa8f5 !important;
// 			font-size: 20px;
// 			box-shadow: none !important; /* 去掉阴影 */
// 			border-radius:8px;
// 			background-color: #182137!important;
// 		}
:deep(.el-table){
  --el-table-border-color: #fff;
  --el-table-row-hover-bg-color: #fff;
  --el-table-header-bg-color: #fff;
  --el-table-tr-bg-color: #fff;
  // background-color:#182137;
  --el-table-header-text-color: #000;
  font-size: 16px;
  // color: #D3D6DD;

}
:deep(.el-checkbox__inner){
  background-color: transparent;
  border-radius: 0px;
  border-color: gray;
}
:deep(.el-pagination){
  position: absolute;
  bottom: 50px;
  right: 40px;
  --el-pagination-font-size: 20px;
  // --el-pagination-button-color: #629DE3;
  // --el-pagination-button-bg-color: #182137
}
.search_inp{
margin-right: 20px;
}
.table-container {
  padding-right: 40px;
  .table-top{
    margin-top: 20px;
  }
  .table-srarch {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 24px;
    font-weight: bold;
    color: black;
    .title_b{
      display: flex;
      align-items: center;
      margin-top: 20px;
    font-size: 16px;
    font-weight: normal;
    color: rgb(46, 45, 45);
      
    }

  }
  .search_bom{
    margin-top: 40px;
    display: flex;
    align-items: center;
    .upload-demo{
      display: flex;
      align-items: center;
    }
  }
  .table_Data{
    width: 100%;
    margin-top: 40px;
  }
  .bomBtn{
			width: 100%;
			padding-right: 18px;
			position: absolute;
			bottom: 0;
			right: 0;
			font-size: 20px;
			@include flexCenter;
			.bomBtn_l{
				// cursor: pointer;
				@include flexCenter;
				img{
					width: 38px;
				}
			}
		}
}
</style>