<template>
  <div class="table-container">
    <div class="leftTop">
      <!-- <img src="../../../../assets/img/bank/BK_2.png" alt="">
      {{ route.query.shortName }} -->
    </div>
    <div class="table-srarch">
      <div class="srarch_title">
        <div class="title_t">全国管理员权限管理</div>
      </div>


    </div>
    <div class="search_bom">
      <!-- <el-input class="search_inp" size="large" :suffix-icon="Search" v-model="filterText" style="width: 300px"
        placeholder="搜索姓名、工号" />
      <el-button size="large" @click="queryList">查询</el-button> -->
      <el-button type="primary" @click="state.dialogFormVisible = true" size="large">添加人员</el-button>
    </div>

    <div class="table_Data">
      <el-table class="transparent-table" :data="state.tableData" style="width: 100%">

        <el-table-column prop="name" fixed label="姓名" width="320" />
        <el-table-column prop="jobNumber" label="联通工号(7位数字)" width="220" />
        <el-table-column prop="telephone" label="手机号" width="220" />
        <el-table-column prop="orgName" label="部门" width="320" />
        <el-table-column fixed="right" label="操作" min-width="180">
          <template #default="scope">
            <el-button class="fs20" link type="primary" @click="handleClick(scope.row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :page-size="state.pageObj.pageSize" @size-change="sizeChangeFn"
        :current-page="state.pageObj.pageNo" @current-change="currentChangeFn" background
        layout="total, sizes, prev, pager, next, jumper" :total="state.pageObj.total" />
    </div>
    <el-dialog :close-on-click-modal="false" :before-close="handleClose" v-model="state.dialogFormVisible" title="添加人员" width="500">

      <el-form ref="ruleFormRef" :rules="state.rules" class="formDia" size="large" :model="state.form">
        <el-form-item label="姓名" prop="name" :label-width="state.formLabelWidth">
          <el-input v-model="state.form.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="联通工号(7位数字)" prop="jobNumber" :label-width="state.formLabelWidth">
          <el-input v-model="state.form.jobNumber" autocomplete="off" />
        </el-form-item>
        <el-form-item label="手机号" prop="telephone" :label-width="state.formLabelWidth">
          <el-input v-model="state.form.telephone" autocomplete="off" />
        </el-form-item>
        <el-form-item label="部门" prop="orgName" :label-width="state.formLabelWidth">
          <el-input v-model="state.form.orgName" autocomplete="off" />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetForm(ruleFormRef)">取消</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>


</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import { queryDataFilePermissions, addDataFilePermissions, deleteDataFilePermissions } from '@/script/api/common/commomApi'
import type { FormInstance } from 'element-plus'
import { ElMessage,ElMessageBox } from 'element-plus'

const route = useRoute();
const filterText = ref('')
const ruleFormRef = ref<FormInstance>()
const state = reactive({
  multipleSelection: [],
  tableData: [
    {
      date: '侯健',
      name: '中讯邮电咨询设计院',
      name1: '0922925',
      name2: '18601359585',
      address: '2024-10-11 10:31:23',
    },
    {
      date: '杨猛',
      name: '联通集团建设发展部',
      name1: '0598581',
      name2: '18601100270',
      address: '2024-10-11 10:31:23',
    },

  ],
  pageObj: {
    pageSize: 10,
    pageNo: 1,
    total: 0,
  },
  form: {
    name: '',
    jobNumber: '',
    telephone: '',
    orgName: '',
  },
  dialogFormVisible: false,
  formLabelWidth: '140px',
  rules: {
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      // { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
    ],
    jobNumber: [
      { required: true, message: '请输入工号', trigger: 'blur' },
    ],
    telephone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
    ],
  }
})
// 默认查询
onMounted(() => {
  queryList()
})
// 列表查询
const queryList = async () => {
  const params = {
    projectId: 0,
    type: 3,
    pageNo: state.pageObj.pageNo,
    pageSize: state.pageObj.pageSize,
  }
  const res = await queryDataFilePermissions(params)
  state.tableData = res.data.list
  state.pageObj.total = res.data.total
  console.log(res, 'listlistlistlist');

}
// 新增表单提交
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      addDataFilePermissionsFn()
      
    } else {
      console.log('error submit!', fields)
    }
  })
}
// 重置按钮
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  state.dialogFormVisible = false
}
// 新增接口
const addDataFilePermissionsFn = async () => {
  const params = {
    projectId: 0,
    type: 3,
    ...state.form
  }
  const res = await addDataFilePermissions(params)
  if (res.status === 0) {
    ElMessage({
    message: '新增成功',
    type: 'success',
    })
    queryList()
    resetForm(ruleFormRef.value)
  }
  console.log(res,'resresresresresresres');
}
// 点击关闭按钮
const handleClose = () => {
  resetForm(ruleFormRef.value)
  
}
// 分页器条数
const sizeChangeFn = (e: any) => {
  state.pageObj.pageSize = e
  queryList()
}
// 分页器页码
const currentChangeFn = (e: any) => {
  state.pageObj.pageNo = e
  queryList()
}
// 删除
const handleClick = (e: any) => {
  ElMessageBox.confirm(
    '确定要删除吗?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async ()  => {
        const res = await deleteDataFilePermissions({id:e.id})
      if (res.status === 0) {
      ElMessage({
        message:'删除成功',
        type: 'success',
      })
      queryList()
    }
    })
    .catch(() => {
    })

}
</script>
<style lang="scss" scoped>
@mixin flexCenter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-breadcrumb__inner) {
  --el-text-color-regular: #D3D6DD !important;
  --el-text-color-primary: #D3D6DD !important;
  font-weight: normal !important;
  font-size: 20px !important;
}

:deep(.el-breadcrumb__separator) {
  color: #D3D6DD !important;
}

// :deep( .search_inp .el-input__wrapper){
// 		border: 1px solid #4aa8f5 !important;
// 			font-size: 20px;
// 			box-shadow: none !important; /* 去掉阴影 */
// 			border-radius:8px;
// 			background-color: #182137!important;
// 		}
:deep(.el-table) {
  --el-table-border-color: #fff;
  --el-table-row-hover-bg-color: #fff;
  --el-table-header-bg-color: #fff;
  --el-table-tr-bg-color: #fff;
  // background-color:#182137;
  --el-table-header-text-color: #000;
  font-size: 20px;
  // color: #D3D6DD;

}

:deep(.el-checkbox__inner) {
  background-color: transparent;
  border-radius: 0px;
  border-color: gray;
}

:deep(.el-pagination) {
  position: absolute;
  bottom: 50px;
  right: 40px;
  --el-pagination-font-size: 20px;
  // --el-pagination-button-color: #629DE3;
  // --el-pagination-button-bg-color: #182137
}

.search_inp {
  margin-right: 20px;
}

.table-container {
  padding-top: 20px;
  padding-left: 20px;

  .leftTop {
    color: #000;

    img {
      display: block;
      margin-right: 15px;
    }

    font-size: 20px;
    display: flex;
    align-items: center;
  }

  .table-srarch {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 24px;
    font-weight: bold;
    color: black;

    .title_b {
      display: flex;
      align-items: center;
      margin-top: 20px;
      font-size: 16px;
      font-weight: normal;
      color: rgb(46, 45, 45);

    }

  }

  .search_bom {
    margin-top: 40px;
  }

  .table_Data {
    width: 100%;
    margin-top: 40px;
  }

  .bomBtn {
    width: 100%;
    padding-right: 18px;
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 20px;
    @include flexCenter;

    .bomBtn_l {
      // cursor: pointer;
      @include flexCenter;

      img {
        width: 38px;
      }
    }
  }
}

.fs20 {
  font-size: 20px;
}

.formDia {
  margin-right: 30px;
}
</style>