<template>
  <div ref="pageContainerRef" class="project-management-main">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <div class="title-icon"></div>
        <span>项目管理</span>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <im-search-form
        class="search-form"
        :columns="state.columnSearchOptions"
        :base-height="28"
        :col-height="40"
        :selectWidth="'233px'"
        :search-style="{ width: '50%', borderRadius: '1px' }"
        :col-num-per-row="5"
        @on-search="searchHandler"
        @on-height-change="handleFormHeightChange"
        default-values-on-reset
        search-on-expand
      />
    </div>



    <!-- 项目表格 -->
    <div class="table-section">
      <im-table
        ref="tableRef"
        :data="state.tableData"
        :columns="tableColumns"
        :loading="state.tableLoading"
        :pagination="state.tablePage"
        :height="tableHeight"
        :column-storage="createColumnStorage('project_management', 'local')"
        stripe
        border
        center
        show-checkbox
        show-overflow-tooltip
        toolbar
        @on-page-change="handlePageChange"
        @on-page-size-change="handlePageSizeChange"
        @on-reload="refreshTableData"
        @selection-change="rowSelectHandler"
        @sort-change="sortChangeHandler"
      >
        <!-- 工具栏左侧 -->
        <template #toolbar-left="{ checkedRows }">
          <el-button
            type="primary"
            size="small"
            @click="handleAddProject"
          >
            添加项目
          </el-button>
          <el-button
            size="small"
            @click="handleExportProject"
          >
            导出项目
          </el-button>
          <!-- <el-button
            type="primary"
            size="small"
            :disabled="checkedRows.length === 0"
            @click="handleBatchExport(checkedRows)"
          >
            批量导出
          </el-button> -->
        </template>

        <!-- 项目编号列 -->
        <template #projectCode="{ row }">
          <span class="project-code-text">{{ row.projectCode }}</span>
        </template>

        <!-- 项目名称列 -->
        <template #projectName="{ row }">
          <el-link
            type="primary"
            @click="handleViewProjectDetail(row)"
            class="project-name-link"
          >
            {{ row.projectName }}
          </el-link>
        </template>

        <!-- 立项批复文号列 -->
        <template #lxSn="{ row }">
          <span class="approval-number-text">{{ row.lxSn }}</span>
        </template>

        <!-- 项目经理列 -->
        <template #managerName="{ row }">
          <span class="project-manager-text">{{ row.managerName }}</span>
        </template>

        <!-- 所属阶段列 -->
        <template #proState="{ row }">
          <el-tag
            :type="getStageTagType(row.proState)"
            size="small"
            effect="dark"
          >
            {{ row.proState || '' }}
          </el-tag>
        </template>

        <!-- 创建时间列 -->
        <template #createdTime="{ row }">
          <span class="create-time-text">{{ row.createdTime }}</span>
        </template>

        <!-- 更新人列 -->
        <template #updatedBy="{ row }">
          <span class="updater-text">{{ row.updatedBy }}</span>
        </template>

        <!-- 最后更新时间列 -->
        <template #updatedTime="{ row }">
          <span class="last-update-time-text">{{ row.updatedTime }}</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div class="operation-buttons">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleViewProjectDetail(row)"
            >
              项目详情
            </el-button>
          </div>
        </template>
      </im-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, onUnmounted, getCurrentInstance, h } from "vue";
import { ImTableInstance, createColumnStorage } from "@/components/ItsmCommon";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  queryProjectPage,
  exportProjectData,
  type ProjectData,
  type ProjectQueryParams
} from '../api';

const { $message } = getCurrentInstance().appContext.config.globalProperties;

// 定义事件
const emit = defineEmits(["jump-to"]);

// 表格引用
const tableRef = ref<ImTableInstance>();

// 页面容器引用
const pageContainerRef = ref<HTMLDivElement>();

// 根据页面高度设置表格高度（使用响应式状态）
const tableHeight = computed(() => state.tableHeight);

// 数据对象
const state = reactive({
  columnSearchOptions: [],
  tableData: [] as ProjectData[],
  tableLoading: false,
  tablePage: {
    hideOnEmptyData: true,
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  conditions: [],
  remoteSort: {} as any,
  selectedRows: [] as ProjectData[],
  searchFormHeight: 32,
  tableHeight: 400, // 添加表格高度状态
});

// 响应式数据引用（直接使用state.xxx）

/**
 * 表格列配置 - 基于新的数据结构
 */
const tableColumns = [
  {
    prop: 'projectCode',
    label: '项目编号',
    minWidth: 180,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 200,
    align: 'left',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'lxSn',
    label: '立项批复文号',
    minWidth: 180,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'managerName',
    label: '项目经理',
    minWidth: 120,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'proState',
    label: '所属阶段',
    minWidth: 140,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'createdTime',
    label: '创建时间',
    minWidth: 160,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'updatedBy',
    label: '更新人',
    minWidth: 100,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'updatedTime',
    label: '最新更新时间',
    minWidth: 160,
    align: 'center',
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'operation',
    label: '操作',
    minWidth: 120,
    align: 'center',
    fixed: 'right',
    sortable: false,
    showOverflowTooltip: false
  }
];

// 加载查询条件数据
const loadQueryConditions = async () => {
  try {
    // 保持原来的搜索字段配置，只调整field映射
    const searchFields = [
      {
        field: "projectCodeOrName", // 对应API的projectCodeOrName字段
        name: "项目编号/名称",
        component: "Input"
      },
      {
        field: "lxSn", // 对应API的lxSn字段（立项批复文号）
        name: "立项批复文号",
        component: "Input"
      },
      {
        field: "lxInvest", // 对应API的lxInvest字段（立项批复总投资）
        name: "预算金额",
        component: "Input"
      },
      {
        field: "managerName", // 对应API的managerName字段（项目经理姓名）
        name: "项目经理",
        component: "Input"
      }
    ];

    state.columnSearchOptions = searchFields;
  } catch (error) {
    console.error('Load query conditions error:', error);
  }
};

// 加载表格数据 - 使用真实API
const loadTableData = async () => {
  state.tableLoading = true;
  try {
    // 构建查询参数
    const queryParams: ProjectQueryParams = {
      conditions: state.conditions,
      pageNum: state.tablePage.currentPage,
      pageSize: state.tablePage.pageSize
    };

    // 添加排序参数
    if (state.remoteSort.orderBy) {
      queryParams.orderBy = state.remoteSort.orderBy;
      queryParams.descType = state.remoteSort.descType;
    }

    console.log('Loading table data with params:', queryParams);

    // 调用真实API
    const response = await queryProjectPage(queryParams);
    if (response.status === "0") {
      state.tableData = response.data.list;
      state.tablePage.total = response.data.total;
      console.log('API response received, total:', response.data.total, 'list length:', response.data.list.length);
    } else {
      throw new Error(response.msg);
    }

  } catch (error) {
    console.error('Load table data error:', error);
    $message.error('加载项目数据失败');
  } finally {
    state.tableLoading = false;
  }
};

// 刷新表格数据
const refreshTableData = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
};

// 检索触发 - 处理搜索条件
const searchHandler = async (conditions: any) => {
  // 转换搜索条件格式以匹配API要求
  const apiConditions = [];

  if (conditions && conditions.length > 0) {
    for (const condition of conditions) {
      if (condition.value && condition.value.trim()) {
        // 特殊处理项目编号/名称搜索
        if (condition.field === 'projectCodeOrName') {
          apiConditions.push({
            value: condition.value.trim(),
            field: 'projectCodeOrName',
            fuzzyable: true,
            operator: 'fuzzy'
          });
        } else {
          // 其他字段的搜索条件
          apiConditions.push({
            value: condition.value.trim(),
            field: condition.field,
            fuzzyable: true,
            operator: 'fuzzy'
          });
        }
      }
    }
  }

  state.conditions = apiConditions;
  state.remoteSort = {};
  tableRef.value?.getTable().clearSort();
  await refreshTableData();
};

// 查询表单高度变更触发
const handleFormHeightChange = (height: number) => {
  state.searchFormHeight = height;
  // 重新计算表格高度
  setTimeout(() => {
    state.tableHeight = calculatePreciseTableHeight();
  }, 50);
};

// 页码变更 - 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
const handlePageChange = async (pageOrSize: number, pageSize: number, query?: any) => {
  console.log('ProjectManagement - 分页变化:', { pageOrSize, pageSize, query });
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== state.tablePage.pageSize) {
    // 页码变化
    console.log('ProjectManagement - 页码变化:', pageOrSize);
    state.tablePage.currentPage = pageOrSize;
    await loadTableData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('ProjectManagement - 分页大小变化(在page-change中)，跳过处理');
  }
};

// 分页大小变更 - 参数顺序：(currentPage, size, query)
const handlePageSizeChange = async (currentPage: number, size: number, query?: any) => {
  console.log('ProjectManagement - 分页大小变化:', { currentPage, size, query });
  state.tablePage.pageSize = size;
  state.tablePage.currentPage = 1; // 分页大小变化时重置到第一页
  await loadTableData();
};

// 表格行选中触发
const rowSelectHandler = (rows: ProjectData[]) => {
  state.selectedRows = rows;
};

// 排序改变触发
const sortChangeHandler = ({ order, prop }) => {
  if (order) {
    state.remoteSort = { descType: order === 'ascending' ? 'asc' : 'desc', orderBy: prop };
  } else {
    state.remoteSort = {};
  }
  refreshTableData();
};

// 获取阶段标签类型
const getStageTagType = (stage: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const stageMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    // 实际已知的阶段
    '建设中': 'primary',      // 蓝色 - 表示正在进行中
    '设计阶段': 'warning',    // 橙色 - 表示设计准备阶段

    // 其他可能的阶段（保留以备扩展）
    '立项批复': 'info',
    '可研批复': 'info',
    '设计批复': 'warning',
    '工程施工': 'primary',
    '初验': 'warning',
    '终验': 'success',
    '验收交付': 'success',
    '交付结算': 'success',
    '已完成': 'success',
    '进行中': 'primary',
    '待开始': 'info',
    '暂停': 'warning',
    '异常': 'danger'
  };
  return stageMap[stage] || 'info';
};

// 查看项目详情
const handleViewProjectDetail = (row: ProjectData) => {
  // 构造传递给项目详情页面的数据 - 映射到新的数据结构
  const jumpData = {
    projectId: row.projectId,
    projectCode: row.projectCode,
    projectName: row.projectName,
    approvalNumber: row.lxSn, // 立项批复文号
    projectManager: row.managerName, // 项目经理姓名
    budgetAmount: row.lxInvest ? `${(row.lxInvest / 10000).toFixed(2)}万元` : '', // 立项批复总投资
    projectStage: row.proState, // 项目状态
    createTime: row.createdTime, // 创建时间
    updater: row.updatedBy, // 更新人
    lastUpdateTime: row.updatedTime, // 更新时间
    projectStatus: row.proState, // 项目状态
    proState: row.proState, // 添加 proState 字段用于详情页显示
    // 传递完整的项目数据以便详情页使用
    fullData: row
  };

  console.log('ProjectManagementMain: 跳转到项目详情，传递数据:', jumpData);
  emit('jump-to', 'projectDetailView', jumpData);
};

// 添加项目
const handleAddProject = () => {
  // 导入并打开添加项目弹窗
  import('./AddProjectDialog.vue').then((module: any) => {
    const AddProjectDialog = module.default || module;
    import('@/components/ReDialog').then(({ addDialog, closeDialog }) => {
      const dialogOptions = {
        title: '添加项目',
        width: '500px',
        contentRenderer: ({ options, index }: any) => h(AddProjectDialog, {
          onSave: (projectData: any) => {
            // 先关闭弹窗
            closeDialog(options, index);
            // 然后处理项目添加成功
            handleProjectAdded(projectData);
          },
          onCancel: () => {
            // 关闭弹窗
            closeDialog(options, index);
          }
        }),
        hideFooter: true
      };

      addDialog(dialogOptions);
    });
  });
};

// 项目添加成功回调
const handleProjectAdded = (projectData: any) => {
  console.log('ProjectManagementMain: 项目添加成功，数据:', projectData);

  // 显示成功消息（弹窗组件已经显示过了，这里可以不重复显示）
  // $message.success('项目添加成功');

  // 刷新表格数据以显示新添加的项目
  refreshTableData();
};

// 导出项目
const handleExportProject = async () => {
  try {


    // 使用与查询接口相同的参数格式
    const exportParams: ProjectQueryParams = {
      conditions: state.conditions, // 使用当前的搜索条件
    };

    // 注意：当前导出基于搜索条件，如需要基于选中行导出，需要后端支持ID筛选

    await exportProjectData(exportParams);
  } catch (error) {
    $message.error('导出失败');
    console.error('Export error:', error);
  }
};

// 批量导出
const handleBatchExport = async (rows: ProjectData[]) => {
  try {
    if (rows.length === 0) {
      $message.warning('请选择要导出的数据');
      return;
    }
    $message.success('数据正在导出中...');

    // 使用与查询接口相同的参数格式
    const exportParams: ProjectQueryParams = {
      conditions: state.conditions, // 使用当前的搜索条件
      pageNum: 1,
      pageSize: 999999 // 导出时使用大的pageSize以获取所有数据
    };

    // 注意：当前导出基于搜索条件，如需要基于选中行导出，需要后端支持ID筛选

    await exportProjectData(exportParams);
  } catch (error) {
    $message.error('导出失败');
    console.error('Export error:', error);
  }
};

// 精确计算表格高度的方法
const calculatePreciseTableHeight = () => {
  // 使用视窗高度减去固定元素高度
  const viewportHeight = window.innerHeight;

  // 计算固定高度元素：
  // 1. 页面padding: 24px * 2 = 48px
  // 2. 页面标题: 18px + 24px(margin) = 42px
  // 3. 搜索区域: searchFormHeight + 16px*2(padding) + 2px(border) + 16px(margin) = searchFormHeight + 50px
  // 4. 表格容器padding: 16px * 2 = 32px
  // 5. 表格工具栏: 50px
  // 6. 表格头部: 40px
  // 7. 分页区域: 60px
  // 8. 预留空间: 30px

  const fixedHeights = {
    pagePadding: 48,
    pageHeader: 42,
    searchSection: state.searchFormHeight + 50,
    tablePadding: 32,
    tableToolbar: 50,
    tableHeader: 40,
    pagination: 60,
    reserved: 30
  };

  const totalFixedHeight = Object.values(fixedHeights).reduce((sum, height) => sum + height, 0);
  const calculatedHeight = viewportHeight - totalFixedHeight;

  // 确保最小高度为300px
  return Math.max(calculatedHeight, 300);
};

// 窗口大小变化处理
const handleResize = () => {
  // 强制重新计算高度
  state.tableHeight = calculatePreciseTableHeight();
};

// 挂载初始化
onMounted(async () => {
  await loadQueryConditions();
  await loadTableData();

  // 等待DOM渲染完成后计算精确高度
  setTimeout(() => {
    state.tableHeight = calculatePreciseTableHeight();
  }, 100);

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 组件选项
defineOptions({
  name: 'ProjectManagementMain'
});
</script>

<style lang="scss" scoped>
.project-management-main {
  height: 100%;
  background: rgb(19, 24, 41);
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;

      .title-icon {
        width: 4px;
        height: 18px;
        background: #409eff;
        border-radius: 2px;
      }
    }
  }

  .search-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }



  .table-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);

    :deep(.el-table) {
      background: transparent;
      color: #ffffff;

      .el-table__header-wrapper {
        .el-table__header {
          background: rgba(255, 255, 255, 0.05);

          th {
            background: transparent;
            color: #ffffff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
          }
        }
      }

      // .el-table__body-wrapper {
      //   .el-table__body {
      //     tr {
      //       background: transparent;

      //       &:hover {
      //         background: rgba(255, 255, 255, 0.05) !important;
      //       }

      //       td {
      //         background: transparent;
      //         color: #ffffff;
      //         border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      //       }
      //     }

      //     .el-table__row--striped {
      //       background: rgba(255, 255, 255, 0.02);

      //       td {
      //         background: transparent;
      //       }
      //     }
      //   }
      // }
    }

    .project-code-text,
    .approval-number-text,
    .project-manager-text,
    .project-stage-text,
    .create-time-text,
    .updater-text,
    .last-update-time-text {
      color: #ffffff;
      font-size: 14px;
    }

    .project-name-link {
      color: #409eff;
      text-decoration: none;

      &:hover {
        color: #66b1ff;
      }
    }

    .operation-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
        color: #ffffff;
      }
    }
  }
}

// 全局样式覆盖
:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__jump,
  .el-pager li {
    color: #ffffff;
  }

  .el-pagination__sizes .el-select .el-input__inner {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }

  .el-pagination__jump .el-input__inner {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }
}

:deep(.el-button) {
  &.el-button--primary {
    background: #409eff;
    border-color: #409eff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }

  &.el-button--default {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 阶段标签样式
:deep(.el-tag) {
  &.el-tag--primary {
    background: rgba(64, 158, 255, 0.2);
    border-color: rgba(64, 158, 255, 0.3);
    color: #409eff;
  }

  &.el-tag--success {
    background: rgba(103, 194, 58, 0.2);
    border-color: rgba(103, 194, 58, 0.3);
    color: #67c23a;
  }

  &.el-tag--warning {
    background: rgba(230, 162, 60, 0.2);
    border-color: rgba(230, 162, 60, 0.3);
    color: #e6a23c;
  }

  &.el-tag--info {
    background: rgba(144, 147, 153, 0.2);
    border-color: rgba(144, 147, 153, 0.3);
    color: #909399;
  }

  &.el-tag--danger {
    background: rgba(245, 108, 108, 0.2);
    border-color: rgba(245, 108, 108, 0.3);
    color: #f56c6c;
  }
}
</style>
