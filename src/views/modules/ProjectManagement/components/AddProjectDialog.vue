<template>
  <div class="add-project-dialog">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="add-project-form"
    >
      <el-form-item label="项目编号" prop="projectCode">
        <el-select
          v-model="formData.projectCode"
          placeholder="请选择项目编号"
          filterable
          clearable
          @change="handleProjectCodeChange"
          class="form-select"
        >
          <el-option
            v-for="item in projectOptions"
            :key="item.projectCode"
            :label="item.projectCode"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="formData.projectName"
          placeholder="请选择项目名称"
          filterable
          clearable
          @change="handleProjectNameChange"
          class="form-select"
        >
          <el-option
            v-for="item in projectOptions"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="handleConfirm"
      >
        确认
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, getCurrentInstance } from 'vue';
import { queryNoShowProjectList, addProjectByHand, type ProjectOption } from '../api';

const { $message } = getCurrentInstance().appContext.config.globalProperties;

// 定义事件
const emit = defineEmits(['save', 'cancel']);

// 表单引用
const formRef = ref();

// 提交加载状态
const submitLoading = ref(false);

// 项目选项列表
const projectOptions = ref<ProjectOption[]>([]);

// 表单数据
const formData = reactive({
  projectCode: '',
  projectName: ''
});

// 表单验证规则
const formRules = {
  projectCode: [
    { required: true, message: '请选择项目编号', trigger: 'change' }
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' }
  ]
};

// 加载项目选项列表
const loadProjectOptions = async () => {
  try {
    const response = await queryNoShowProjectList();
    if (response.status === '0') {
      projectOptions.value = response.data;
      console.log('加载项目选项成功:', response.data);
    } else {
      throw new Error(response.msg);
    }
  } catch (error) {
    console.error('加载项目选项失败:', error);
    $message.error('加载项目列表失败');
  }
};

// 项目编号变化处理
const handleProjectCodeChange = (value: string) => {
  if (value) {
    // 根据选择的项目编号自动选择对应的项目名称
    const selectedProject = projectOptions.value.find(item => item.projectCode === value);
    if (selectedProject) {
      formData.projectName = selectedProject.projectName;
    }
  } else {
    // 清空时，同时清空项目名称
    formData.projectName = '';
  }
};

// 项目名称变化处理
const handleProjectNameChange = (value: string) => {
  if (value) {
    // 根据选择的项目名称自动选择对应的项目编号
    const selectedProject = projectOptions.value.find(item => item.projectName === value);
    if (selectedProject) {
      formData.projectCode = selectedProject.projectCode;
    }
  } else {
    // 清空时，同时清空项目编号
    formData.projectCode = '';
  }
};

// 确认添加
const handleConfirm = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      // $message.error('添加项目失败');
      return;
    }

    submitLoading.value = true;

    // 调用添加项目接口
    const response = await addProjectByHand(formData.projectCode);
    if (response.status === '0') {
      $message.success('项目添加成功');
      emit('save', {
        projectCode: formData.projectCode,
        projectName: formData.projectName
      });
    } else {
      $message.error('添加项目失败');
      throw new Error(response.msg);
    }
  } catch (error) {
    console.error('添加项目失败:', error);
    
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};

// 组件挂载时加载数据
onMounted(() => {
  loadProjectOptions();
});

// 组件选项
defineOptions({
  name: 'AddProjectDialog'
});
</script>

<style lang="scss" scoped>
.add-project-dialog {
  padding: 20px;

  .add-project-form {
    .form-select {
      width: 100%;
    }

    :deep(.el-form-item__label) {
      color: #ffffff;
    }

    :deep(.el-select) {
      .el-input__inner {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      .el-input__suffix {
        .el-select__caret {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .el-button {
      &.el-button--default {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }
      }

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}

// 下拉框选项样式
:deep(.el-select-dropdown) {
  background: rgba(30, 35, 50, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .el-select-dropdown__item {
    color: #ffffff;
    background: transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #ffffff;
    }
  }
}
</style>
