import axios from 'axios'
import qs from 'qs'
import { ElMessage } from 'element-plus'
import { userStore } from '@/store/modules/userBigScreen'
import router from '@/router'
import { inflate } from 'zlib'
import { formatToken, getToken } from "@/utils/auth";
// axios实例
const service = axios.create({
	baseURL: import.meta.env.VITE_BASE_URL,
	timeout: 15000,
	headers: { 'Content-Type': 'application/json;charset=UTF-8' },
})

// 请求拦截器
service.interceptors.request.use(
	(config: any) => {
		// 临时测试
		// authorization   91462f54154e4f15addb8969f4c53b1d   临时测试
		// config.headers.Authorization = '91462f54154e4f15addb8969f4c53b1d'
		config.headers.Authorization = formatToken(getToken().accessToken || getToken().refreshToken);
		config.url = '/BigScreenProjects/' + config.url

		// 对下载接口做无限制超时处理
		if (config.url.substring(0, config.url.indexOf("?")) === 'archivesFile/download' ||  'archivesFile/delete') {
			config.timeout = Infinity
		}
		if (config.url.substring(0, config.url.indexOf("d")) === 'archivesFile/uploa') {
			config.headers = { 'Content-Type': 'multipart/form-data' }
			
			config.timeout = Infinity
			// 'Accept':'application/json, text/plain, */*'
		}
			// multipart/form-data
		const userStoreInstance = userStore()
		const Token = localStorage.getItem('token')
		if (Token) {
			//config.headers.Authorization = userStoreInstance.token
			config.headers.Authorization = Token || userStoreInstance.token
		}
		// 追加时间戳，防止GET请求缓存
		if (config.method?.toUpperCase() === 'GET') {
			config.params = { ...config.params, t: new Date().getTime() }
		}

		if (Object.values(config.headers).includes('application/x-www-form-urlencoded')) {
			config.data = qs.stringify(config.data)
		}

		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

// 响应拦截器
service.interceptors.response.use(
	(response) => {
		if (response.status !== 200) {
			return Promise.reject(new Error(response.statusText || 'Error'))
		}
		console.log('响应成功', response)
		const res = response.data
		// 响应成功
		//  
		if (res.status == 0 || Object.prototype.toString.call(res) === '[object Blob]') {
			res.status = +res.status;
			return res
		}

		// 错误提示
		ElMessage.error(res.msg)

		// 没有权限，如：未登录、登录过期等，需要跳转到登录页
		// if (res.status === 401) {
		// 	router.push({
		// 		path: '/login',
		// 	})
		// 	location.reload()
		// }

		return Promise.reject(new Error(res.msg || 'Error'))
	},
	(error) => {
		ElMessage.error(error.message)
		return Promise.reject(error)
	}
)

// 导出 axios 实例
export default service
